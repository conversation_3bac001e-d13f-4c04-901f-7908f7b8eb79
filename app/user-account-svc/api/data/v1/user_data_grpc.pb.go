// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: data/v1/user_data.proto

package datav1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DataService_Get_FullMethodName    = "/data.v1.DataService/Get"
	DataService_Update_FullMethodName = "/data.v1.DataService/Update"
)

// DataServiceClient is the client API for DataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// === 用户数据服务接口 ===
type DataServiceClient interface {
	// 获取用户的私有数据，文档不存在时返回空
	// Get /api/user-account/data/v1/DataService/Get
	Get(ctx context.Context, in *GetDataReq, opts ...grpc.CallOption) (*GetDataRes, error)
	// 修改用户的私有数据
	// POST /api/user-account/data/v1/DataService/Update
	Update(ctx context.Context, in *UpdateDataReq, opts ...grpc.CallOption) (*UpdateDataRes, error)
}

type dataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataServiceClient(cc grpc.ClientConnInterface) DataServiceClient {
	return &dataServiceClient{cc}
}

func (c *dataServiceClient) Get(ctx context.Context, in *GetDataReq, opts ...grpc.CallOption) (*GetDataRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDataRes)
	err := c.cc.Invoke(ctx, DataService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataServiceClient) Update(ctx context.Context, in *UpdateDataReq, opts ...grpc.CallOption) (*UpdateDataRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDataRes)
	err := c.cc.Invoke(ctx, DataService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataServiceServer is the server API for DataService service.
// All implementations must embed UnimplementedDataServiceServer
// for forward compatibility.
//
// === 用户数据服务接口 ===
type DataServiceServer interface {
	// 获取用户的私有数据，文档不存在时返回空
	// Get /api/user-account/data/v1/DataService/Get
	Get(context.Context, *GetDataReq) (*GetDataRes, error)
	// 修改用户的私有数据
	// POST /api/user-account/data/v1/DataService/Update
	Update(context.Context, *UpdateDataReq) (*UpdateDataRes, error)
	mustEmbedUnimplementedDataServiceServer()
}

// UnimplementedDataServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDataServiceServer struct{}

func (UnimplementedDataServiceServer) Get(context.Context, *GetDataReq) (*GetDataRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedDataServiceServer) Update(context.Context, *UpdateDataReq) (*UpdateDataRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedDataServiceServer) mustEmbedUnimplementedDataServiceServer() {}
func (UnimplementedDataServiceServer) testEmbeddedByValue()                     {}

// UnsafeDataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataServiceServer will
// result in compilation errors.
type UnsafeDataServiceServer interface {
	mustEmbedUnimplementedDataServiceServer()
}

func RegisterDataServiceServer(s grpc.ServiceRegistrar, srv DataServiceServer) {
	// If the following call pancis, it indicates UnimplementedDataServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DataService_ServiceDesc, srv)
}

func _DataService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServiceServer).Get(ctx, req.(*GetDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServiceServer).Update(ctx, req.(*UpdateDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DataService_ServiceDesc is the grpc.ServiceDesc for DataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "data.v1.DataService",
	HandlerType: (*DataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Get",
			Handler:    _DataService_Get_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _DataService_Update_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "data/v1/user_data.proto",
}
