// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: data/v1/user_data.proto

package datav1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// === 数据结构 ===
// 用户的json文档，每个文档限制最大256kb
type UserKV struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KeyPath       string                 `protobuf:"bytes,1,opt,name=key_path,json=keyPath,proto3" json:"key_path,omitempty" dc:"以 dot 分隔的键路径，例如 'settings.theme.color'"` // 以 dot 分隔的键路径，例如 "settings.theme.color"
	ValueData     string                 `protobuf:"bytes,2,opt,name=value_data,json=valueData,proto3" json:"value_data,omitempty" dc:"对应键的 JSON 数据值，支持任意结构"`             // 对应键的 JSON 数据值，支持任意结构
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserKV) Reset() {
	*x = UserKV{}
	mi := &file_data_v1_user_data_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserKV) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserKV) ProtoMessage() {}

func (x *UserKV) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserKV.ProtoReflect.Descriptor instead.
func (*UserKV) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{0}
}

func (x *UserKV) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

func (x *UserKV) GetValueData() string {
	if x != nil {
		return x.ValueData
	}
	return ""
}

// 获取用户数据
// 用户键路径，使用 dot 分隔的 JSON 路径结构。
//
// 规则说明：
// - `key_path` 的第一级表示一个独立的 JSON 文档名（如 "settings"），限制最大256kb
// - 后续部分表示文档内的字段路径（如 "settings.theme.color" 表示 settings 文档中的 $.theme.color）
//
// 示例：
// - key_path = "settings"              → 返回完整 settings JSON 文档
// - key_path = "settings.theme"        → 返回 settings 文档中 $.theme 的值
// - key_path = "settings.theme.color"  → 返回 settings 文档中 $.theme.color 的值
//
// 更新请求遵循相同规则。
type GetDataReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KeyPath       string                 `protobuf:"bytes,1,opt,name=key_path,json=keyPath,proto3" json:"key_path,omitempty" dc:"以 dot 分隔的键路径，例如 'settings.theme.color'"` // 以 dot 分隔的键路径，例如 "settings.theme.color"
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDataReq) Reset() {
	*x = GetDataReq{}
	mi := &file_data_v1_user_data_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataReq) ProtoMessage() {}

func (x *GetDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataReq.ProtoReflect.Descriptor instead.
func (*GetDataReq) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{1}
}

func (x *GetDataReq) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

type GetDataRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UserKV                `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDataRes) Reset() {
	*x = GetDataRes{}
	mi := &file_data_v1_user_data_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDataRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataRes) ProtoMessage() {}

func (x *GetDataRes) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataRes.ProtoReflect.Descriptor instead.
func (*GetDataRes) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{2}
}

func (x *GetDataRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetDataRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetDataRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetDataRes) GetData() *UserKV {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新数据
// 用户键路径，使用 dot 分隔的 JSON 路径结构。
//
// 规则说明：
// - `key_path` 的第一级表示一个独立的 JSON 文档名（如 "settings"），限制最大256kb
// - 后续部分表示文档内的字段路径（如 "settings.theme.color" 表示 settings 文档中的 $.theme.color）
//
// 示例：
// - key_path = "settings"              → 覆盖 JSON 文档
// - key_path = "settings.theme"        → 更新 $.theme 的值
// - key_path = "settings.theme.color"  → 更新 $.theme.color 的值
//
// 清空文档
// - key_path = "settings"
// - value_data = "{}"
type UpdateDataReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KeyPath       string                 `protobuf:"bytes,1,opt,name=key_path,json=keyPath,proto3" json:"key_path,omitempty" dc:"以 dot 分隔的键路径，例如 'settings.theme.color'"`                                       // 以 dot 分隔的键路径，例如 "settings.theme.color"
	ValueData     string                 `protobuf:"bytes,2,opt,name=value_data,json=valueData,proto3" json:"value_data,omitempty" dc:"对应键的 JSON 数据值，数组的数据以'['开头，json object数据以'{'开头，布尔值是true false, null表示空值"` // 对应键的 JSON 数据值，数组的数据以'['开头，json object数据以'{'开头，布尔值是true false, null表示空值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDataReq) Reset() {
	*x = UpdateDataReq{}
	mi := &file_data_v1_user_data_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDataReq) ProtoMessage() {}

func (x *UpdateDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDataReq.ProtoReflect.Descriptor instead.
func (*UpdateDataReq) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDataReq) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

func (x *UpdateDataReq) GetValueData() string {
	if x != nil {
		return x.ValueData
	}
	return ""
}

type UpdateDataRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDataRes) Reset() {
	*x = UpdateDataRes{}
	mi := &file_data_v1_user_data_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDataRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDataRes) ProtoMessage() {}

func (x *UpdateDataRes) ProtoReflect() protoreflect.Message {
	mi := &file_data_v1_user_data_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDataRes.ProtoReflect.Descriptor instead.
func (*UpdateDataRes) Descriptor() ([]byte, []int) {
	return file_data_v1_user_data_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDataRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateDataRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateDataRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_data_v1_user_data_proto protoreflect.FileDescriptor

const file_data_v1_user_data_proto_rawDesc = "" +
	"\n" +
	"\x17data/v1/user_data.proto\x12\adata.v1\x1a\x11common/base.proto\"B\n" +
	"\x06UserKV\x12\x19\n" +
	"\bkey_path\x18\x01 \x01(\tR\akeyPath\x12\x1d\n" +
	"\n" +
	"value_data\x18\x02 \x01(\tR\tvalueData\"'\n" +
	"\n" +
	"GetDataReq\x12\x19\n" +
	"\bkey_path\x18\x01 \x01(\tR\akeyPath\"|\n" +
	"\n" +
	"GetDataRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12#\n" +
	"\x04data\x18\x04 \x01(\v2\x0f.data.v1.UserKVR\x04data\"I\n" +
	"\rUpdateDataReq\x12\x19\n" +
	"\bkey_path\x18\x01 \x01(\tR\akeyPath\x12\x1d\n" +
	"\n" +
	"value_data\x18\x02 \x01(\tR\tvalueData\"Z\n" +
	"\rUpdateDataRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error2x\n" +
	"\vDataService\x12/\n" +
	"\x03Get\x12\x13.data.v1.GetDataReq\x1a\x13.data.v1.GetDataRes\x128\n" +
	"\x06Update\x12\x16.data.v1.UpdateDataReq\x1a\x16.data.v1.UpdateDataResB3Z1halalplus/app/user-account-svc/api/data/v1;datav1b\x06proto3"

var (
	file_data_v1_user_data_proto_rawDescOnce sync.Once
	file_data_v1_user_data_proto_rawDescData []byte
)

func file_data_v1_user_data_proto_rawDescGZIP() []byte {
	file_data_v1_user_data_proto_rawDescOnce.Do(func() {
		file_data_v1_user_data_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_data_v1_user_data_proto_rawDesc), len(file_data_v1_user_data_proto_rawDesc)))
	})
	return file_data_v1_user_data_proto_rawDescData
}

var file_data_v1_user_data_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_data_v1_user_data_proto_goTypes = []any{
	(*UserKV)(nil),        // 0: data.v1.UserKV
	(*GetDataReq)(nil),    // 1: data.v1.GetDataReq
	(*GetDataRes)(nil),    // 2: data.v1.GetDataRes
	(*UpdateDataReq)(nil), // 3: data.v1.UpdateDataReq
	(*UpdateDataRes)(nil), // 4: data.v1.UpdateDataRes
	(*common.Error)(nil),  // 5: common.Error
}
var file_data_v1_user_data_proto_depIdxs = []int32{
	5, // 0: data.v1.GetDataRes.error:type_name -> common.Error
	0, // 1: data.v1.GetDataRes.data:type_name -> data.v1.UserKV
	5, // 2: data.v1.UpdateDataRes.error:type_name -> common.Error
	1, // 3: data.v1.DataService.Get:input_type -> data.v1.GetDataReq
	3, // 4: data.v1.DataService.Update:input_type -> data.v1.UpdateDataReq
	2, // 5: data.v1.DataService.Get:output_type -> data.v1.GetDataRes
	4, // 6: data.v1.DataService.Update:output_type -> data.v1.UpdateDataRes
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_data_v1_user_data_proto_init() }
func file_data_v1_user_data_proto_init() {
	if File_data_v1_user_data_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_data_v1_user_data_proto_rawDesc), len(file_data_v1_user_data_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_data_v1_user_data_proto_goTypes,
		DependencyIndexes: file_data_v1_user_data_proto_depIdxs,
		MessageInfos:      file_data_v1_user_data_proto_msgTypes,
	}.Build()
	File_data_v1_user_data_proto = out.File
	file_data_v1_user_data_proto_goTypes = nil
	file_data_v1_user_data_proto_depIdxs = nil
}
