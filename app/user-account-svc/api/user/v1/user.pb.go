// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user/v1/user.proto

package userv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Gender int32

const (
	Gender_UNKNOWN Gender = 0
	Gender_MALE    Gender = 1
	Gender_FEMALE  Gender = 2
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[0].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[0]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

// 验证码用途场景定义
type VerifyCodeScene int32

const (
	// 未指定场景，后端应拒绝处理该请求
	VerifyCodeScene_VERIFY_CODE_SCENE_UNSPECIFIED VerifyCodeScene = 0
	// 用户登录时使用的验证码（手机号登录、验证码登录）
	VerifyCodeScene_LOGIN VerifyCodeScene = 1
	// 用户注册时使用的验证码（注册新账号）
	VerifyCodeScene_SIGN_UP VerifyCodeScene = 2
	// 重置密码时使用的验证码（找回/忘记密码）
	VerifyCodeScene_RESET_PASSWORD VerifyCodeScene = 3
	// 绑定手机号时使用的验证码（用于增强账户安全或添加手机号）
	VerifyCodeScene_BIND_PHONE VerifyCodeScene = 4
)

// Enum value maps for VerifyCodeScene.
var (
	VerifyCodeScene_name = map[int32]string{
		0: "VERIFY_CODE_SCENE_UNSPECIFIED",
		1: "LOGIN",
		2: "SIGN_UP",
		3: "RESET_PASSWORD",
		4: "BIND_PHONE",
	}
	VerifyCodeScene_value = map[string]int32{
		"VERIFY_CODE_SCENE_UNSPECIFIED": 0,
		"LOGIN":                         1,
		"SIGN_UP":                       2,
		"RESET_PASSWORD":                3,
		"BIND_PHONE":                    4,
	}
)

func (x VerifyCodeScene) Enum() *VerifyCodeScene {
	p := new(VerifyCodeScene)
	*p = x
	return p
}

func (x VerifyCodeScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyCodeScene) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[1].Descriptor()
}

func (VerifyCodeScene) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[1]
}

func (x VerifyCodeScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyCodeScene.Descriptor instead.
func (VerifyCodeScene) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

// 验证码发送渠道
type VerifyCodeChannel int32

const (
	// 未指定发送渠道，表示非法请求，后端应返回错误
	VerifyCodeChannel_UNSPECIFIED VerifyCodeChannel = 0
	// 通过短信（SMS）发送验证码。适用于大多数用户，尤其是国内用户。
	VerifyCodeChannel_SMS VerifyCodeChannel = 1
	// 通过 WhatsApp 发送验证码。适用于国际用户或不支持短信的区域。
	VerifyCodeChannel_WHATSAPP VerifyCodeChannel = 2
	// 通过邮箱发送验证码。适用于邮箱注册/找回密码等场景。
	VerifyCodeChannel_EMAIL VerifyCodeChannel = 3
	// 通过语音电话播报验证码。适用于用户无法接收短信时的兜底方式。
	VerifyCodeChannel_VOICE VerifyCodeChannel = 4
)

// Enum value maps for VerifyCodeChannel.
var (
	VerifyCodeChannel_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "SMS",
		2: "WHATSAPP",
		3: "EMAIL",
		4: "VOICE",
	}
	VerifyCodeChannel_value = map[string]int32{
		"UNSPECIFIED": 0,
		"SMS":         1,
		"WHATSAPP":    2,
		"EMAIL":       3,
		"VOICE":       4,
	}
)

func (x VerifyCodeChannel) Enum() *VerifyCodeChannel {
	p := new(VerifyCodeChannel)
	*p = x
	return p
}

func (x VerifyCodeChannel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyCodeChannel) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[2].Descriptor()
}

func (VerifyCodeChannel) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[2]
}

func (x VerifyCodeChannel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyCodeChannel.Descriptor instead.
func (VerifyCodeChannel) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

// UserInfo 用户信息结构
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"用户id"`                                                   // 用户id
	Account       string                 `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty" dc:"账号"`                                            // 账号
	CreateTime    int64                  `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty" dc:"注册时间"`                 // 注册时间
	Nickname      string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty" dc:"昵称"`                                          // 昵称
	Gender        Gender                 `protobuf:"varint,5,opt,name=gender,proto3,enum=user.v1.Gender" json:"gender,omitempty" dc:"性别：0未知 1男 2女"`               // 性别：0未知 1男 2女
	Avatar        string                 `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty" dc:"头像url"`                                           // 头像url
	AreaCode      string                 `protobuf:"bytes,7,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty" dc:"国家码（如 '62'）"`                 // 国家码（如 "62"）
	PhoneNum      string                 `protobuf:"bytes,8,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty" dc:"展示用户当前手机号码（中间部分脱敏，仅保留首尾各两位）"` // 展示用户当前手机号码（中间部分脱敏，仅保留首尾各两位）
	BindEmail     bool                   `protobuf:"varint,9,opt,name=bind_email,json=bindEmail,proto3" json:"bind_email,omitempty" dc:"是否绑定email（0/1）"`          // 是否绑定email（0/1）
	BindPhone     bool                   `protobuf:"varint,10,opt,name=bind_phone,json=bindPhone,proto3" json:"bind_phone,omitempty" dc:"是否绑定手机（0/1）"`            // 是否绑定手机（0/1）
	BindRealName  bool                   `protobuf:"varint,11,opt,name=bind_real_name,json=bindRealName,proto3" json:"bind_real_name,omitempty" dc:"是否实名认证（0/1）"` // 是否实名认证（0/1）
	FirstName     string                 `protobuf:"bytes,12,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	MiddleName    string                 `protobuf:"bytes,13,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`
	LastName      string                 `protobuf:"bytes,14,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_user_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserInfo) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_UNKNOWN
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *UserInfo) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *UserInfo) GetBindEmail() bool {
	if x != nil {
		return x.BindEmail
	}
	return false
}

func (x *UserInfo) GetBindPhone() bool {
	if x != nil {
		return x.BindPhone
	}
	return false
}

func (x *UserInfo) GetBindRealName() bool {
	if x != nil {
		return x.BindRealName
	}
	return false
}

func (x *UserInfo) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UserInfo) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *UserInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// 注册请求
type SignUpReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty" dc:"用户名/手机号/邮箱"`     // 用户名/手机号/邮箱
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty" dc:"密码（加密/明文视业务）"` // 密码（加密/明文视业务）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignUpReq) Reset() {
	*x = SignUpReq{}
	mi := &file_user_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUpReq) ProtoMessage() {}

func (x *SignUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUpReq.ProtoReflect.Descriptor instead.
func (*SignUpReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *SignUpReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SignUpReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// 注册响应
type SignUpRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignUpRes) Reset() {
	*x = SignUpRes{}
	mi := &file_user_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignUpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUpRes) ProtoMessage() {}

func (x *SignUpRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUpRes.ProtoReflect.Descriptor instead.
func (*SignUpRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *SignUpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SignUpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignUpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 登录请求
type SignInReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty" dc:"用户名/手机号/邮箱"` // 用户名/手机号/邮箱
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty" dc:"密码"`       // 密码
	FrontInfo     *common.FrontInfo      `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInReq) Reset() {
	*x = SignInReq{}
	mi := &file_user_v1_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInReq) ProtoMessage() {}

func (x *SignInReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInReq.ProtoReflect.Descriptor instead.
func (*SignInReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *SignInReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SignInReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SignInReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

// 登录响应
type UserSignInRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UserSignInResData     `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserSignInRes) Reset() {
	*x = UserSignInRes{}
	mi := &file_user_v1_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSignInRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSignInRes) ProtoMessage() {}

func (x *UserSignInRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSignInRes.ProtoReflect.Descriptor instead.
func (*UserSignInRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *UserSignInRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserSignInRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserSignInRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserSignInRes) GetData() *UserSignInResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserSignInResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"` // 登录成功后返回的会话 token
	UserInfo      *UserInfo              `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserSignInResData) Reset() {
	*x = UserSignInResData{}
	mi := &file_user_v1_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSignInResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSignInResData) ProtoMessage() {}

func (x *UserSignInResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSignInResData.ProtoReflect.Descriptor instead.
func (*UserSignInResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *UserSignInResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UserSignInResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

// 登录（用户名）
type SignInByAccountReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty" dc:"用户名/手机号/邮箱"` // 用户名/手机号/邮箱
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty" dc:"密码"`       // 密码
	FrontInfo     *common.FrontInfo      `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInByAccountReq) Reset() {
	*x = SignInByAccountReq{}
	mi := &file_user_v1_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInByAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByAccountReq) ProtoMessage() {}

func (x *SignInByAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByAccountReq.ProtoReflect.Descriptor instead.
func (*SignInByAccountReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *SignInByAccountReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SignInByAccountReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SignInByAccountReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SignInByAccountRes struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *SignInByAccountResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInByAccountRes) Reset() {
	*x = SignInByAccountRes{}
	mi := &file_user_v1_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInByAccountRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByAccountRes) ProtoMessage() {}

func (x *SignInByAccountRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByAccountRes.ProtoReflect.Descriptor instead.
func (*SignInByAccountRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *SignInByAccountRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SignInByAccountRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignInByAccountRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SignInByAccountRes) GetData() *SignInByAccountResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SignInByAccountResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"`           // 登录成功后返回的会话 token
	Secret        string                 `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty" dc:"客户端存储的密钥，用于api签名和更新token"` // 客户端存储的密钥，用于api签名和更新token
	UserInfo      *UserInfo              `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInByAccountResData) Reset() {
	*x = SignInByAccountResData{}
	mi := &file_user_v1_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInByAccountResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByAccountResData) ProtoMessage() {}

func (x *SignInByAccountResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByAccountResData.ProtoReflect.Descriptor instead.
func (*SignInByAccountResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{8}
}

func (x *SignInByAccountResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SignInByAccountResData) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *SignInByAccountResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

// 注册/登录方式：手机号短信验证（默认）
//
// 流程：
// 1. 用户输入手机号 (交互：纯数字键盘)
//   - 国际区号：+62（目前只开放+62）
//
// 2. 检查手机号格式
//   - 输入时实时校验：08 开头、长度 10～12 位
//
// 3. 判断是否为虚拟号/黑名单（通过服务端接口验证）
// 4. 请求发送验证码
//   - 冷却控制、防多次点击（60 秒倒计时）
//
// 5. 输入验证码
// 6. 验证成功 → 登录成功 / 注册新用户
type SignInByPhoneReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneInfo     *common.PhoneInfo      `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty"`
	OptCode       string                 `protobuf:"bytes,2,opt,name=opt_code,json=optCode,proto3" json:"opt_code,omitempty"`
	FrontInfo     *common.FrontInfo      `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInByPhoneReq) Reset() {
	*x = SignInByPhoneReq{}
	mi := &file_user_v1_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInByPhoneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByPhoneReq) ProtoMessage() {}

func (x *SignInByPhoneReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByPhoneReq.ProtoReflect.Descriptor instead.
func (*SignInByPhoneReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{9}
}

func (x *SignInByPhoneReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

func (x *SignInByPhoneReq) GetOptCode() string {
	if x != nil {
		return x.OptCode
	}
	return ""
}

func (x *SignInByPhoneReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SignInByPhoneRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *SignInByPhoneResData  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInByPhoneRes) Reset() {
	*x = SignInByPhoneRes{}
	mi := &file_user_v1_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInByPhoneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByPhoneRes) ProtoMessage() {}

func (x *SignInByPhoneRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByPhoneRes.ProtoReflect.Descriptor instead.
func (*SignInByPhoneRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{10}
}

func (x *SignInByPhoneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SignInByPhoneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignInByPhoneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SignInByPhoneRes) GetData() *SignInByPhoneResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SignInByPhoneResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"`           // 登录成功后返回的会话 token
	Secret        string                 `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty" dc:"客户端存储的密钥，用于api签名和更新token"` // 客户端存储的密钥，用于api签名和更新token
	UserInfo      *UserInfo              `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	SessionId     string                 `protobuf:"bytes,4,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty" dc:"客户端存储，用于api签名和更新token"` // 客户端存储，用于api签名和更新token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignInByPhoneResData) Reset() {
	*x = SignInByPhoneResData{}
	mi := &file_user_v1_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInByPhoneResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByPhoneResData) ProtoMessage() {}

func (x *SignInByPhoneResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByPhoneResData.ProtoReflect.Descriptor instead.
func (*SignInByPhoneResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{11}
}

func (x *SignInByPhoneResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SignInByPhoneResData) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *SignInByPhoneResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *SignInByPhoneResData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// 手机已注册检查
type PhoneValidCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneInfo     *common.PhoneInfo      `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PhoneValidCheckReq) Reset() {
	*x = PhoneValidCheckReq{}
	mi := &file_user_v1_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhoneValidCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneValidCheckReq) ProtoMessage() {}

func (x *PhoneValidCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneValidCheckReq.ProtoReflect.Descriptor instead.
func (*PhoneValidCheckReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{12}
}

func (x *PhoneValidCheckReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

type PhoneValidCheckRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PhoneValidCheckRes) Reset() {
	*x = PhoneValidCheckRes{}
	mi := &file_user_v1_user_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhoneValidCheckRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneValidCheckRes) ProtoMessage() {}

func (x *PhoneValidCheckRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneValidCheckRes.ProtoReflect.Descriptor instead.
func (*PhoneValidCheckRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{13}
}

func (x *PhoneValidCheckRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PhoneValidCheckRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PhoneValidCheckRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type RefreshTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FrontInfo     *common.FrontInfo      `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenReq) Reset() {
	*x = RefreshTokenReq{}
	mi := &file_user_v1_user_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenReq) ProtoMessage() {}

func (x *RefreshTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenReq.ProtoReflect.Descriptor instead.
func (*RefreshTokenReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{14}
}

func (x *RefreshTokenReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type RefreshTokenRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *RefreshTokenResData   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenRes) Reset() {
	*x = RefreshTokenRes{}
	mi := &file_user_v1_user_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRes) ProtoMessage() {}

func (x *RefreshTokenRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRes.ProtoReflect.Descriptor instead.
func (*RefreshTokenRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{15}
}

func (x *RefreshTokenRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RefreshTokenRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RefreshTokenRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RefreshTokenRes) GetData() *RefreshTokenResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type RefreshTokenResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenResData) Reset() {
	*x = RefreshTokenResData{}
	mi := &file_user_v1_user_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResData) ProtoMessage() {}

func (x *RefreshTokenResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResData.ProtoReflect.Descriptor instead.
func (*RefreshTokenResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{16}
}

func (x *RefreshTokenResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type UserInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfoReq) Reset() {
	*x = UserInfoReq{}
	mi := &file_user_v1_user_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoReq) ProtoMessage() {}

func (x *UserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoReq.ProtoReflect.Descriptor instead.
func (*UserInfoReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{17}
}

type UserInfoRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UserInfoResData       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfoRes) Reset() {
	*x = UserInfoRes{}
	mi := &file_user_v1_user_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoRes) ProtoMessage() {}

func (x *UserInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoRes.ProtoReflect.Descriptor instead.
func (*UserInfoRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{18}
}

func (x *UserInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserInfoRes) GetData() *UserInfoResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserInfoResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserInfo      *UserInfo              `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfoResData) Reset() {
	*x = UserInfoResData{}
	mi := &file_user_v1_user_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoResData) ProtoMessage() {}

func (x *UserInfoResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoResData.ProtoReflect.Descriptor instead.
func (*UserInfoResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{19}
}

func (x *UserInfoResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type UpdateUserInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FirstName     string                 `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	MiddleName    string                 `protobuf:"bytes,2,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`
	LastName      string                 `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Nickname      string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty" dc:"昵称"`                            // 昵称
	Gender        Gender                 `protobuf:"varint,5,opt,name=gender,proto3,enum=user.v1.Gender" json:"gender,omitempty" dc:"性别：0未知 1男 2女"` // 性别：0未知 1男 2女
	Avatar        string                 `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty" dc:"头像url"`                             // 头像url
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserInfoReq) Reset() {
	*x = UpdateUserInfoReq{}
	mi := &file_user_v1_user_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoReq) ProtoMessage() {}

func (x *UpdateUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateUserInfoReq) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UpdateUserInfoReq) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *UpdateUserInfoReq) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UpdateUserInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateUserInfoReq) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_UNKNOWN
}

func (x *UpdateUserInfoReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type UpdateUserInfoRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserInfoRes) Reset() {
	*x = UpdateUserInfoRes{}
	mi := &file_user_v1_user_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoRes) ProtoMessage() {}

func (x *UpdateUserInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoRes.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateUserInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateUserInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 请求结构：发送验证码
type SendVerifyCodeReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 手机信息（包含手机号和国家码等）
	PhoneInfo *common.PhoneInfo `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty" dc:"手机信息（包含手机号和国家码等）"`
	// 指定发送验证码的渠道（短信、WhatsApp、语音等）
	VerifyCodeChannel VerifyCodeChannel `protobuf:"varint,2,opt,name=verify_code_channel,json=verifyCodeChannel,proto3,enum=user.v1.VerifyCodeChannel" json:"verify_code_channel,omitempty" dc:"指定发送验证码的渠道（短信、WhatsApp、语音等）"`
	// 指定验证码使用的业务场景（登录、注册、重置密码等）
	VerifyCodeScene VerifyCodeScene `protobuf:"varint,3,opt,name=verify_code_scene,json=verifyCodeScene,proto3,enum=user.v1.VerifyCodeScene" json:"verify_code_scene,omitempty" dc:"指定验证码使用的业务场景（登录、注册、重置密码等）"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SendVerifyCodeReq) Reset() {
	*x = SendVerifyCodeReq{}
	mi := &file_user_v1_user_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeReq) ProtoMessage() {}

func (x *SendVerifyCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeReq.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{22}
}

func (x *SendVerifyCodeReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

func (x *SendVerifyCodeReq) GetVerifyCodeChannel() VerifyCodeChannel {
	if x != nil {
		return x.VerifyCodeChannel
	}
	return VerifyCodeChannel_UNSPECIFIED
}

func (x *SendVerifyCodeReq) GetVerifyCodeScene() VerifyCodeScene {
	if x != nil {
		return x.VerifyCodeScene
	}
	return VerifyCodeScene_VERIFY_CODE_SCENE_UNSPECIFIED
}

// 响应结构：发送验证码结果
type SendVerifyCodeRes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 状态码，0 表示成功，其他为错误码（用于简单兼容处理）
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty" dc:"状态码，0 表示成功，其他为错误码（用于简单兼容处理）"`
	// 状态消息，一般用于错误描述或提示信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty" dc:"状态消息，一般用于错误描述或提示信息"`
	// 通用错误结构，包含错误码、错误描述、详细字段错误等（可选）
	Error         *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty" dc:"通用错误结构，包含错误码、错误描述、详细字段错误等（可选）"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyCodeRes) Reset() {
	*x = SendVerifyCodeRes{}
	mi := &file_user_v1_user_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeRes) ProtoMessage() {}

func (x *SendVerifyCodeRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeRes.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{23}
}

func (x *SendVerifyCodeRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendVerifyCodeRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SendVerifyCodeRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_user_v1_user_proto protoreflect.FileDescriptor

const file_user_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x12user/v1/user.proto\x12\auser.v1\x1a\x17common/phone_info.proto\x1a\x17common/front_info.proto\x1a\x11common/base.proto\"\xad\x03\n" +
	"\bUserInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x18\n" +
	"\aaccount\x18\x02 \x01(\tR\aaccount\x12\x1f\n" +
	"\vcreate_time\x18\x03 \x01(\x03R\n" +
	"createTime\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\x12'\n" +
	"\x06gender\x18\x05 \x01(\x0e2\x0f.user.v1.GenderR\x06gender\x12\x16\n" +
	"\x06avatar\x18\x06 \x01(\tR\x06avatar\x12\x1b\n" +
	"\tarea_code\x18\a \x01(\tR\bareaCode\x12\x1b\n" +
	"\tphone_num\x18\b \x01(\tR\bphoneNum\x12\x1d\n" +
	"\n" +
	"bind_email\x18\t \x01(\bR\tbindEmail\x12\x1d\n" +
	"\n" +
	"bind_phone\x18\n" +
	" \x01(\bR\tbindPhone\x12$\n" +
	"\x0ebind_real_name\x18\v \x01(\bR\fbindRealName\x12\x1d\n" +
	"\n" +
	"first_name\x18\f \x01(\tR\tfirstName\x12\x1f\n" +
	"\vmiddle_name\x18\r \x01(\tR\n" +
	"middleName\x12\x1b\n" +
	"\tlast_name\x18\x0e \x01(\tR\blastName\"A\n" +
	"\tSignUpReq\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"V\n" +
	"\tSignUpRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"s\n" +
	"\tSignInReq\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x120\n" +
	"\n" +
	"front_info\x18\x03 \x01(\v2\x11.common.FrontInfoR\tfrontInfo\"\x8a\x01\n" +
	"\rUserSignInRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12.\n" +
	"\x04data\x18\x04 \x01(\v2\x1a.user.v1.UserSignInResDataR\x04data\"Y\n" +
	"\x11UserSignInResData\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12.\n" +
	"\tuser_info\x18\x03 \x01(\v2\x11.user.v1.UserInfoR\buserInfo\"|\n" +
	"\x12SignInByAccountReq\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x120\n" +
	"\n" +
	"front_info\x18\x03 \x01(\v2\x11.common.FrontInfoR\tfrontInfo\"\x94\x01\n" +
	"\x12SignInByAccountRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x123\n" +
	"\x04data\x18\x04 \x01(\v2\x1f.user.v1.SignInByAccountResDataR\x04data\"v\n" +
	"\x16SignInByAccountResData\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x16\n" +
	"\x06secret\x18\x02 \x01(\tR\x06secret\x12.\n" +
	"\tuser_info\x18\x03 \x01(\v2\x11.user.v1.UserInfoR\buserInfo\"\x91\x01\n" +
	"\x10SignInByPhoneReq\x120\n" +
	"\n" +
	"phone_info\x18\x01 \x01(\v2\x11.common.PhoneInfoR\tphoneInfo\x12\x19\n" +
	"\bopt_code\x18\x02 \x01(\tR\aoptCode\x120\n" +
	"\n" +
	"front_info\x18\x03 \x01(\v2\x11.common.FrontInfoR\tfrontInfo\"\x90\x01\n" +
	"\x10SignInByPhoneRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x121\n" +
	"\x04data\x18\x04 \x01(\v2\x1d.user.v1.SignInByPhoneResDataR\x04data\"\x93\x01\n" +
	"\x14SignInByPhoneResData\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x16\n" +
	"\x06secret\x18\x02 \x01(\tR\x06secret\x12.\n" +
	"\tuser_info\x18\x03 \x01(\v2\x11.user.v1.UserInfoR\buserInfo\x12\x1d\n" +
	"\n" +
	"session_id\x18\x04 \x01(\tR\tsessionId\"F\n" +
	"\x12PhoneValidCheckReq\x120\n" +
	"\n" +
	"phone_info\x18\x01 \x01(\v2\x11.common.PhoneInfoR\tphoneInfo\"_\n" +
	"\x12PhoneValidCheckRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"C\n" +
	"\x0fRefreshTokenReq\x120\n" +
	"\n" +
	"front_info\x18\x03 \x01(\v2\x11.common.FrontInfoR\tfrontInfo\"\x8e\x01\n" +
	"\x0fRefreshTokenRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x120\n" +
	"\x04data\x18\x04 \x01(\v2\x1c.user.v1.RefreshTokenResDataR\x04data\"+\n" +
	"\x13RefreshTokenResData\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\r\n" +
	"\vUserInfoReq\"\x86\x01\n" +
	"\vUserInfoRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12,\n" +
	"\x04data\x18\x04 \x01(\v2\x18.user.v1.UserInfoResDataR\x04data\"A\n" +
	"\x0fUserInfoResData\x12.\n" +
	"\tuser_info\x18\x03 \x01(\v2\x11.user.v1.UserInfoR\buserInfo\"\xcd\x01\n" +
	"\x11UpdateUserInfoReq\x12\x1d\n" +
	"\n" +
	"first_name\x18\x01 \x01(\tR\tfirstName\x12\x1f\n" +
	"\vmiddle_name\x18\x02 \x01(\tR\n" +
	"middleName\x12\x1b\n" +
	"\tlast_name\x18\x03 \x01(\tR\blastName\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\x12'\n" +
	"\x06gender\x18\x05 \x01(\x0e2\x0f.user.v1.GenderR\x06gender\x12\x16\n" +
	"\x06avatar\x18\x06 \x01(\tR\x06avatar\"^\n" +
	"\x11UpdateUserInfoRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"\xd7\x01\n" +
	"\x11SendVerifyCodeReq\x120\n" +
	"\n" +
	"phone_info\x18\x01 \x01(\v2\x11.common.PhoneInfoR\tphoneInfo\x12J\n" +
	"\x13verify_code_channel\x18\x02 \x01(\x0e2\x1a.user.v1.VerifyCodeChannelR\x11verifyCodeChannel\x12D\n" +
	"\x11verify_code_scene\x18\x03 \x01(\x0e2\x18.user.v1.VerifyCodeSceneR\x0fverifyCodeScene\"^\n" +
	"\x11SendVerifyCodeRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error*+\n" +
	"\x06Gender\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\b\n" +
	"\x04MALE\x10\x01\x12\n" +
	"\n" +
	"\x06FEMALE\x10\x02*p\n" +
	"\x0fVerifyCodeScene\x12!\n" +
	"\x1dVERIFY_CODE_SCENE_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05LOGIN\x10\x01\x12\v\n" +
	"\aSIGN_UP\x10\x02\x12\x12\n" +
	"\x0eRESET_PASSWORD\x10\x03\x12\x0e\n" +
	"\n" +
	"BIND_PHONE\x10\x04*Q\n" +
	"\x11VerifyCodeChannel\x12\x0f\n" +
	"\vUNSPECIFIED\x10\x00\x12\a\n" +
	"\x03SMS\x10\x01\x12\f\n" +
	"\bWHATSAPP\x10\x02\x12\t\n" +
	"\x05EMAIL\x10\x03\x12\t\n" +
	"\x05VOICE\x10\x042\xe6\x04\n" +
	"\vUserService\x12H\n" +
	"\x0eSendVerifyCode\x12\x1a.user.v1.SendVerifyCodeReq\x1a\x1a.user.v1.SendVerifyCodeRes\x120\n" +
	"\x06SignUp\x12\x12.user.v1.SignUpReq\x1a\x12.user.v1.SignUpRes\x124\n" +
	"\x06SignIn\x12\x12.user.v1.SignInReq\x1a\x16.user.v1.UserSignInRes\x12K\n" +
	"\x0fSignInByAccount\x12\x1b.user.v1.SignInByAccountReq\x1a\x1b.user.v1.SignInByAccountRes\x12E\n" +
	"\rSignInByPhone\x12\x19.user.v1.SignInByPhoneReq\x1a\x19.user.v1.SignInByPhoneRes\x12K\n" +
	"\x0fPhoneValidCheck\x12\x1b.user.v1.PhoneValidCheckReq\x1a\x1b.user.v1.PhoneValidCheckRes\x12B\n" +
	"\fRefreshToken\x12\x18.user.v1.RefreshTokenReq\x1a\x18.user.v1.RefreshTokenRes\x126\n" +
	"\bUserInfo\x12\x14.user.v1.UserInfoReq\x1a\x14.user.v1.UserInfoRes\x12H\n" +
	"\x0eUpdateUserInfo\x12\x1a.user.v1.UpdateUserInfoReq\x1a\x1a.user.v1.UpdateUserInfoResB3Z1halalplus/app/user-account-svc/api/user/v1;userv1b\x06proto3"

var (
	file_user_v1_user_proto_rawDescOnce sync.Once
	file_user_v1_user_proto_rawDescData []byte
)

func file_user_v1_user_proto_rawDescGZIP() []byte {
	file_user_v1_user_proto_rawDescOnce.Do(func() {
		file_user_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)))
	})
	return file_user_v1_user_proto_rawDescData
}

var file_user_v1_user_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_user_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_user_v1_user_proto_goTypes = []any{
	(Gender)(0),                    // 0: user.v1.Gender
	(VerifyCodeScene)(0),           // 1: user.v1.VerifyCodeScene
	(VerifyCodeChannel)(0),         // 2: user.v1.VerifyCodeChannel
	(*UserInfo)(nil),               // 3: user.v1.UserInfo
	(*SignUpReq)(nil),              // 4: user.v1.SignUpReq
	(*SignUpRes)(nil),              // 5: user.v1.SignUpRes
	(*SignInReq)(nil),              // 6: user.v1.SignInReq
	(*UserSignInRes)(nil),          // 7: user.v1.UserSignInRes
	(*UserSignInResData)(nil),      // 8: user.v1.UserSignInResData
	(*SignInByAccountReq)(nil),     // 9: user.v1.SignInByAccountReq
	(*SignInByAccountRes)(nil),     // 10: user.v1.SignInByAccountRes
	(*SignInByAccountResData)(nil), // 11: user.v1.SignInByAccountResData
	(*SignInByPhoneReq)(nil),       // 12: user.v1.SignInByPhoneReq
	(*SignInByPhoneRes)(nil),       // 13: user.v1.SignInByPhoneRes
	(*SignInByPhoneResData)(nil),   // 14: user.v1.SignInByPhoneResData
	(*PhoneValidCheckReq)(nil),     // 15: user.v1.PhoneValidCheckReq
	(*PhoneValidCheckRes)(nil),     // 16: user.v1.PhoneValidCheckRes
	(*RefreshTokenReq)(nil),        // 17: user.v1.RefreshTokenReq
	(*RefreshTokenRes)(nil),        // 18: user.v1.RefreshTokenRes
	(*RefreshTokenResData)(nil),    // 19: user.v1.RefreshTokenResData
	(*UserInfoReq)(nil),            // 20: user.v1.UserInfoReq
	(*UserInfoRes)(nil),            // 21: user.v1.UserInfoRes
	(*UserInfoResData)(nil),        // 22: user.v1.UserInfoResData
	(*UpdateUserInfoReq)(nil),      // 23: user.v1.UpdateUserInfoReq
	(*UpdateUserInfoRes)(nil),      // 24: user.v1.UpdateUserInfoRes
	(*SendVerifyCodeReq)(nil),      // 25: user.v1.SendVerifyCodeReq
	(*SendVerifyCodeRes)(nil),      // 26: user.v1.SendVerifyCodeRes
	(*common.Error)(nil),           // 27: common.Error
	(*common.FrontInfo)(nil),       // 28: common.FrontInfo
	(*common.PhoneInfo)(nil),       // 29: common.PhoneInfo
}
var file_user_v1_user_proto_depIdxs = []int32{
	0,  // 0: user.v1.UserInfo.gender:type_name -> user.v1.Gender
	27, // 1: user.v1.SignUpRes.error:type_name -> common.Error
	28, // 2: user.v1.SignInReq.front_info:type_name -> common.FrontInfo
	27, // 3: user.v1.UserSignInRes.error:type_name -> common.Error
	8,  // 4: user.v1.UserSignInRes.data:type_name -> user.v1.UserSignInResData
	3,  // 5: user.v1.UserSignInResData.user_info:type_name -> user.v1.UserInfo
	28, // 6: user.v1.SignInByAccountReq.front_info:type_name -> common.FrontInfo
	27, // 7: user.v1.SignInByAccountRes.error:type_name -> common.Error
	11, // 8: user.v1.SignInByAccountRes.data:type_name -> user.v1.SignInByAccountResData
	3,  // 9: user.v1.SignInByAccountResData.user_info:type_name -> user.v1.UserInfo
	29, // 10: user.v1.SignInByPhoneReq.phone_info:type_name -> common.PhoneInfo
	28, // 11: user.v1.SignInByPhoneReq.front_info:type_name -> common.FrontInfo
	27, // 12: user.v1.SignInByPhoneRes.error:type_name -> common.Error
	14, // 13: user.v1.SignInByPhoneRes.data:type_name -> user.v1.SignInByPhoneResData
	3,  // 14: user.v1.SignInByPhoneResData.user_info:type_name -> user.v1.UserInfo
	29, // 15: user.v1.PhoneValidCheckReq.phone_info:type_name -> common.PhoneInfo
	27, // 16: user.v1.PhoneValidCheckRes.error:type_name -> common.Error
	28, // 17: user.v1.RefreshTokenReq.front_info:type_name -> common.FrontInfo
	27, // 18: user.v1.RefreshTokenRes.error:type_name -> common.Error
	19, // 19: user.v1.RefreshTokenRes.data:type_name -> user.v1.RefreshTokenResData
	27, // 20: user.v1.UserInfoRes.error:type_name -> common.Error
	22, // 21: user.v1.UserInfoRes.data:type_name -> user.v1.UserInfoResData
	3,  // 22: user.v1.UserInfoResData.user_info:type_name -> user.v1.UserInfo
	0,  // 23: user.v1.UpdateUserInfoReq.gender:type_name -> user.v1.Gender
	27, // 24: user.v1.UpdateUserInfoRes.error:type_name -> common.Error
	29, // 25: user.v1.SendVerifyCodeReq.phone_info:type_name -> common.PhoneInfo
	2,  // 26: user.v1.SendVerifyCodeReq.verify_code_channel:type_name -> user.v1.VerifyCodeChannel
	1,  // 27: user.v1.SendVerifyCodeReq.verify_code_scene:type_name -> user.v1.VerifyCodeScene
	27, // 28: user.v1.SendVerifyCodeRes.error:type_name -> common.Error
	25, // 29: user.v1.UserService.SendVerifyCode:input_type -> user.v1.SendVerifyCodeReq
	4,  // 30: user.v1.UserService.SignUp:input_type -> user.v1.SignUpReq
	6,  // 31: user.v1.UserService.SignIn:input_type -> user.v1.SignInReq
	9,  // 32: user.v1.UserService.SignInByAccount:input_type -> user.v1.SignInByAccountReq
	12, // 33: user.v1.UserService.SignInByPhone:input_type -> user.v1.SignInByPhoneReq
	15, // 34: user.v1.UserService.PhoneValidCheck:input_type -> user.v1.PhoneValidCheckReq
	17, // 35: user.v1.UserService.RefreshToken:input_type -> user.v1.RefreshTokenReq
	20, // 36: user.v1.UserService.UserInfo:input_type -> user.v1.UserInfoReq
	23, // 37: user.v1.UserService.UpdateUserInfo:input_type -> user.v1.UpdateUserInfoReq
	26, // 38: user.v1.UserService.SendVerifyCode:output_type -> user.v1.SendVerifyCodeRes
	5,  // 39: user.v1.UserService.SignUp:output_type -> user.v1.SignUpRes
	7,  // 40: user.v1.UserService.SignIn:output_type -> user.v1.UserSignInRes
	10, // 41: user.v1.UserService.SignInByAccount:output_type -> user.v1.SignInByAccountRes
	13, // 42: user.v1.UserService.SignInByPhone:output_type -> user.v1.SignInByPhoneRes
	16, // 43: user.v1.UserService.PhoneValidCheck:output_type -> user.v1.PhoneValidCheckRes
	18, // 44: user.v1.UserService.RefreshToken:output_type -> user.v1.RefreshTokenRes
	21, // 45: user.v1.UserService.UserInfo:output_type -> user.v1.UserInfoRes
	24, // 46: user.v1.UserService.UpdateUserInfo:output_type -> user.v1.UpdateUserInfoRes
	38, // [38:47] is the sub-list for method output_type
	29, // [29:38] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_user_v1_user_proto_init() }
func file_user_v1_user_proto_init() {
	if File_user_v1_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_v1_user_proto_goTypes,
		DependencyIndexes: file_user_v1_user_proto_depIdxs,
		EnumInfos:         file_user_v1_user_proto_enumTypes,
		MessageInfos:      file_user_v1_user_proto_msgTypes,
	}.Build()
	File_user_v1_user_proto = out.File
	file_user_v1_user_proto_goTypes = nil
	file_user_v1_user_proto_depIdxs = nil
}
