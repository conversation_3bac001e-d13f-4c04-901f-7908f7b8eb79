package cmd

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
	"halalplus/app/file-storage-svc/internal/controller/file"
	consul "halalplus/utility/gf-registry-consul"
)

func CORS(r *ghttp.Request) {
	r.Response.CORSDefault()
	r.Middleware.Next()
}

var (
	Main = gcmd.Command{
		Name:  "file-storage-svc",
		Usage: "file-storage-svc",
		Brief: "start file-storage-svc http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {

			registry, err := consul.New(consul.WithAddress("127.0.0.1:8500"))
			if err != nil {
				g.Log().Fatal(context.Background(), err)
				return
			}

			s := g.Server()

			// 设置服务注册信息
			s.SetRegistrar(registry)
			s.<PERSON>("file-storage-svc")

			// 添加健康检查端点
			s.BindHandler("/health", func(r *ghttp.Request) {
				r.Response.WriteJson(g.Map{"status": "UP"})
			})

			s.Group("/aapi", func(group *ghttp.RouterGroup) {
				group.Middleware(ghttp.MiddlewareHandlerResponse, CORS)
				group.Bind(
					file.New(),
				)
			})

			s.Run()
			return nil
		},
	}
)
