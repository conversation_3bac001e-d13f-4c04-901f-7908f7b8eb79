# 视频管理系统后端API设计文档

## 1. 系统概述

这是一个视频内容管理系统（Video CMS），支持多语言（中文、英文、印尼语）的视频平台。系统包含移动端APP和后台管理系统两部分。

### 1.1 核心功能模块
- 视频管理（上传、编辑、发布、下线）
- 视频分类管理
- 视频专题管理
- 用户内容浏览（按专题、分类展示）

## 2. 数据库设计

### 2.1 视频表 (videos)
```sql
- id (主键)
- title (标题，支持多语言)
- description (描述，富文本编辑器)
- creator_name (创建人)
- category_id (分类ID)
- cover_image (封面图片)
- video_file (视频文件)
- languages (支持的语言：ZH/EN/ID)
- status (状态：待发布/已发布/已下线)
- is_recommended (是否推荐)
- view_count (浏览次数)
- share_count (分享次数)
- created_at (创建时间)
- updated_at (修改时间)
- published_at (发布时间)
```

### 2.2 视频分类表 (categories)
```sql
- id (主键)
- name (分类名称，支持多语言)
- description (描述)
- languages (支持的语言)
- video_count (视频数量)
- created_at (创建时间)
```

### 2.3 视频专题表 (topics)
```sql
- id (主键)
- name (专题名称，支持多语言)
- short_title (短标题)
- cover_image (专题封面)
- description (专题描述)
- languages (支持的语言)
- status (状态：启用/禁用)
- sort_order (排序)
- video_count (视频数量)
- view_count (浏览次数)
- share_count (分享次数)
- created_at (创建时间)
```

### 2.4 专题视频关联表 (topic_videos)
```sql
- topic_id (专题ID)
- video_id (视频ID)
- sort_order (排序)
```

## 3. API接口设计

### 3.1 视频管理接口

#### 3.1.1 获取视频列表
```
GET /api/admin/videos
参数：
- title: 视频标题搜索
- status: 状态筛选 (all/pending/published/offline)
- category_id: 分类筛选
- is_recommended: 是否推荐筛选
- created_at: 创建时间筛选
- page: 页码
- per_page: 每页数量

响应：
{
  "data": [
    {
      "id": 1,
      "title": "视频标题",
      "languages": ["ZH", "EN", "ID"],
      "category_name": "分类名称",
      "creator_name": "创建人",
      "view_count": 8902,
      "share_count": 8902,
      "status": "published",
      "created_at": "2035-09-19 9:39:39"
    }
  ],
  "pagination": {...}
}
```

#### 3.1.2 创建视频
```
POST /api/admin/videos
参数：
- creator_name: 创建人 (必填)
- category_id: 分类ID (必填)
- published_at: 发布时间
- cover_image: 封面图片文件
- is_recommended: 是否推荐
- languages: 支持的语言数组
- title: 视频标题 (多语言对象)
- description: 视频描述 (富文本)
- video_file: 视频文件 (mp4/mov格式，最大300MB)

响应：
{
  "message": "创建成功",
  "data": { video详情 }
}
```

#### 3.1.3 批量操作视频
```
POST /api/admin/videos/batch
参数：
- video_ids: 视频ID数组
- action: 操作类型 (recommend/offline/online/delete)

响应：
{
  "message": "操作成功",
  "success_count": 5,
  "failed_count": 0
}
```

#### 3.1.4 视频状态流转
- 新建视频 → 立即发布 → 已发布视频 (可上线)
- 新建视频 → 点击提交 → 待发布视频 (可发布)
- 新建视频 → 保存草稿 → 待提交视频 (草稿箱)

### 3.2 视频分类接口

#### 3.2.1 获取分类列表
```
GET /api/admin/categories
参数：
- name: 分类名称搜索
- page: 页码
- per_page: 每页数量

响应：
{
  "data": [
    {
      "id": 234,
      "name": "教育",
      "languages": ["ZH", "EN", "ID"],
      "video_count": 89,
      "created_at": "2023-01-01"
    }
  ]
}
```

#### 3.2.2 创建分类
```
POST /api/admin/categories
参数：
- name: 分类名称 (多语言对象，必填)
- description: 分类描述

响应：
{
  "message": "创建成功",
  "data": { 分类详情 }
}
```

### 3.3 视频专题接口

#### 3.3.1 获取专题列表
```
GET /api/admin/topics
参数：
- name: 专题名称搜索
- status: 状态筛选
- page: 页码
- per_page: 每页数量

响应：
{
  "data": [
    {
      "id": 233,
      "name": "专题名称",
      "short_title": "短标题",
      "languages": ["ZH", "EN", "ID"],
      "video_count": 23,
      "sort_order": 1,
      "view_count": 8902,
      "status": "enabled",
      "created_at": "2035-09-19 9:39:39"
    }
  ]
}
```

#### 3.3.2 创建专题
```
POST /api/admin/topics
参数：
- name: 专题名称 (必填)
- short_title: 短标题 (必填)
- cover_image: 专题封面
- description: 专题描述
- languages: 支持语言
- status: 状态
- associated_videos: 关联视频ID数组
- excluded_videos: 排除视频ID数组

响应：
{
  "message": "创建成功",
  "data": { 专题详情 }
}
```

### 3.4 移动端API接口

#### 3.4.1 获取首页视频列表
```
GET /api/app/videos
参数：
- language: 语言 (zh/en/id)
- category_id: 分类筛选
- page: 页码

响应：
{
  "data": [
    {
      "id": 1,
      "title": "视频标题",
      "cover_image": "封面图片URL",
      "video_url": "视频URL",
      "view_count": 1234,
      "category_name": "分类名称"
    }
  ]
}
```

#### 3.4.2 获取专题列表
```
GET /api/app/topics
参数：
- language: 语言

响应：
{
  "data": [
    {
      "id": 1,
      "name": "专题名称",
      "cover_image": "专题封面",
      "video_count": 10
    }
  ]
}
```

#### 3.4.3 获取专题下的视频
```
GET /api/app/topics/{id}/videos
参数：
- language: 语言
- page: 页码

响应：
{
  "topic_info": { 专题信息 },
  "videos": [ 视频列表 ]
}
```

## 4. 文件上传规范

### 4.1 视频文件
- 支持格式：mp4, mov
- 文件大小：最大300MB
- 上传状态：上传中(33%)、上传失败、上传成功

### 4.2 图片文件
- 支持格式：JPG, PNG
- 文件大小：最大20MB
- 尺寸要求：800*800像素，仅支持JPG/PNG格式

## 5. 多语言支持

### 5.1 支持语言
- 中文 (ZH)
- 英文 (EN) 
- 印尼语 (ID)

### 5.2 语言切换逻辑
- 前端选择语言后，编辑对应语言内容
- 切换语言时显示当前语言的输入框内容
- 提交时存储多语言对应的内容

## 6. 权限控制

### 6.1 后台管理权限
- 管理员可进行所有操作
- 编辑人员仅可编辑自己创建的内容

### 6.2 API鉴权
- 后台API需要管理员token验证
- 移动端API支持匿名访问

## 7. 数据验证规则

- 视频标题：最多100个字符，支持中英文、数字和特殊字符
- 分类名称：最多60个字符，必填
- 专题名称：必填，支持多语言
- 视频描述：最多100个字符，非必填
- 创建人：最多100个字符，必填

## 8. 草稿箱功能

### 8.1 草稿箱列表
```
GET /api/admin/videos/drafts
参数：
- title: 视频标题搜索
- created_at: 创建时间筛选
- updated_at: 修改时间筛选
- page: 页码

响应：
{
  "data": [
    {
      "id": 1,
      "title": "视频标题",
      "languages": ["ZH", "EN", "ID"],
      "status": "待提交",
      "created_at": "2035-09-19 9:39:39",
      "updated_at": "2035-09-19 9:39:39"
    }
  ]
}
```

### 8.2 草稿操作
- 修改：编辑草稿内容
- 删除草稿：批量删除草稿

## 9. 状态管理

### 9.1 视频状态
- **待提交**：保存为草稿的视频
- **待发布**：提交但未发布的视频
- **已发布**：已发布可上线的视频
- **已下线**：已下线的视频

### 9.2 专题状态
- **启用**：前端显示的专题
- **禁用**：前端不显示的专题

## 10. 搜索和筛选功能

### 10.1 后台搜索
- 视频标题模糊搜索
- 按状态筛选
- 按分类筛选
- 按推荐状态筛选
- 按创建时间范围筛选

### 10.2 移动端筛选
- 按分类浏览视频
- 按专题浏览视频
- 按语言显示内容