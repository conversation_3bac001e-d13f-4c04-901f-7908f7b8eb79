-- 视频管理系统数据库表结构
-- 支持多语言（中文、英文、印尼语）的视频内容管理平台

-- 视频分类表
CREATE TABLE `video_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父分类ID，0表示顶级分类',
  `is_zh` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持中文，0-否，1-是',
  `is_en` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持英文，0-否，1-是',
  `is_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持印尼语，0-否，1-是',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `video_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类下视频数量',
  `cover_image` varchar(500) NOT NULL DEFAULT '' COMMENT '分类封面图片URL',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类负责人ID',
  `creator_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `creator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '创建者姓名',
  `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间戳(毫秒)',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间戳(毫秒)',
  `delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间戳(毫秒)，0表示未删除',
  PRIMARY KEY (`id`),
  KEY `idx_parent_status_sort` (`parent_id`, `status`, `sort_order`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类表';

-- 视频分类多语言表
CREATE TABLE `video_category_languages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` int(11) unsigned NOT NULL COMMENT '分类ID',
  `language_id` tinyint(3) unsigned NOT NULL COMMENT '语言ID：0-中文，1-英文，2-印尼语',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间戳(毫秒)',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间戳(毫秒)',
  `delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间戳(毫秒)，0表示未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_language` (`category_id`, `language_id`, `delete_time`),
  KEY `idx_language_id` (`language_id`),
  KEY `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类多语言表';

-- 视频专题表
CREATE TABLE `video_topics` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `is_zh` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持中文，0-否，1-是',
  `is_en` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持英文，0-否，1-是',
  `is_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持印尼语，0-否，1-是',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `video_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '专题下视频数量',
  `view_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '专题浏览次数',
  `share_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '专题分享次数',
  `cover_image` varchar(500) NOT NULL DEFAULT '' COMMENT '专题封面图片URL',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '专题负责人ID',
  `creator_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `creator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '创建者姓名',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间戳(毫秒)',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间戳(毫秒)',
  `delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间戳(毫秒)，0表示未删除',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频专题表';

-- 视频专题多语言表
CREATE TABLE `video_topic_languages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `topic_id` int(11) unsigned NOT NULL COMMENT '专题ID',
  `language_id` tinyint(3) unsigned NOT NULL COMMENT '语言ID：0-中文，1-英文，2-印尼语',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '专题名称',
  `short_title` varchar(100) NOT NULL DEFAULT '' COMMENT '专题短标题',
  `description` text COMMENT '专题描述',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间戳(毫秒)',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间戳(毫秒)',
  `delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间戳(毫秒)，0表示未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_topic_language` (`topic_id`, `language_id`, `delete_time`),
  KEY `idx_language_id` (`language_id`),
  KEY `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频专题多语言表';

-- 视频主表
CREATE TABLE `videos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `is_zh` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持中文，0-否，1-是',
  `is_en` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持英文，0-否，1-是',
  `is_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否支持印尼语，0-否，1-是',
  `category_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '状态：0-草稿，1-待发布，2-已发布，3-已下线',
  `is_recommended` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否推荐，0-否，1-是',
  `is_top` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否置顶，0-否，1-是',
  `cover_image` varchar(500) NOT NULL DEFAULT '' COMMENT '视频封面图片URL',
  `video_url` varchar(500) NOT NULL DEFAULT '' COMMENT '视频文件URL',
  `video_size` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '视频文件大小(字节)',
  `video_duration` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '视频时长(秒)',
  `video_format` varchar(20) NOT NULL DEFAULT '' COMMENT '视频格式：mp4, mov等',
  `view_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '播放次数',
  `share_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '分享次数',
  `collect_count` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '收藏次数',
  `admin_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '负责人ID',
  `creator_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `creator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '创建者姓名',
  `author` varchar(100) NOT NULL DEFAULT '' COMMENT '视频作者',
  `author_logo` varchar(500) NOT NULL DEFAULT '' COMMENT '作者头像URL',
  `author_auth_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '作者认证状态：0-未认证，1-已认证',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间戳(毫秒)',
  `publish_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '发布时间戳(毫秒)',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间戳(毫秒)',
  `delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间戳(毫秒)，0表示未删除',
  PRIMARY KEY (`id`),
  KEY `idx_category_status_recommended` (`category_id`, `status`, `is_recommended`),
  KEY `idx_status_publish_time` (`status`, `publish_time`),
  KEY `idx_recommended_view_count` (`is_recommended`, `view_count`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频主表';

-- 视频多语言表
CREATE TABLE `video_languages` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `video_id` int(11) unsigned NOT NULL COMMENT '视频ID',
  `language_id` tinyint(3) unsigned NOT NULL COMMENT '语言ID：0-中文，1-英文，2-印尼语',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '视频标题',
  `description` text COMMENT '视频描述(富文本)',
  `keywords` varchar(500) NOT NULL DEFAULT '' COMMENT '关键词，用逗号分隔',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间戳(毫秒)',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间戳(毫秒)',
  `delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间戳(毫秒)，0表示未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_video_language` (`video_id`, `language_id`, `delete_time`),
  KEY `idx_language_id` (`language_id`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频多语言表';