// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/news.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsCategoryListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	Pid        uint32 `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty" dc:"父类id"`                                 //父类id
}

func (x *NewsCategoryListReq) Reset() {
	*x = NewsCategoryListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCategoryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCategoryListReq) ProtoMessage() {}

func (x *NewsCategoryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCategoryListReq.ProtoReflect.Descriptor instead.
func (*NewsCategoryListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{0}
}

func (x *NewsCategoryListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *NewsCategoryListReq) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

type CategoryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ParentId   uint32 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	LanguageId uint32 `protobuf:"varint,3,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty"`
	Name       string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	CoverImgs  string `protobuf:"bytes,5,opt,name=cover_imgs,json=coverImgs,proto3" json:"cover_imgs,omitempty"`
}

func (x *CategoryInfo) Reset() {
	*x = CategoryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryInfo) ProtoMessage() {}

func (x *CategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryInfo.ProtoReflect.Descriptor instead.
func (*CategoryInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{1}
}

func (x *CategoryInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CategoryInfo) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *CategoryInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *CategoryInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CategoryInfo) GetCoverImgs() string {
	if x != nil {
		return x.CoverImgs
	}
	return ""
}

type NewsCategoryListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*CategoryInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NewsCategoryListResData) Reset() {
	*x = NewsCategoryListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCategoryListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCategoryListResData) ProtoMessage() {}

func (x *NewsCategoryListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCategoryListResData.ProtoReflect.Descriptor instead.
func (*NewsCategoryListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{2}
}

func (x *NewsCategoryListResData) GetList() []*CategoryInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsCategoryListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *NewsCategoryListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *NewsCategoryListRes) Reset() {
	*x = NewsCategoryListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCategoryListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCategoryListRes) ProtoMessage() {}

func (x *NewsCategoryListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCategoryListRes.ProtoReflect.Descriptor instead.
func (*NewsCategoryListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{3}
}

func (x *NewsCategoryListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCategoryListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCategoryListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsCategoryListRes) GetData() *NewsCategoryListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsListByCateIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CateId      uint32 `protobuf:"varint,1,opt,name=cate_id,json=cateId,proto3" json:"cate_id,omitempty" dc:"分类id"`                        // 分类id
	LanguageId  uint32 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`            // 语言id
	IsRecommend uint32 `protobuf:"varint,3,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty" dc:"是否推荐,0-否,1-是"` // 是否推荐,0-否,1-是
}

func (x *NewsListByCateIdReq) Reset() {
	*x = NewsListByCateIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsListByCateIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByCateIdReq) ProtoMessage() {}

func (x *NewsListByCateIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByCateIdReq.ProtoReflect.Descriptor instead.
func (*NewsListByCateIdReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{4}
}

func (x *NewsListByCateIdReq) GetCateId() uint32 {
	if x != nil {
		return x.CateId
	}
	return 0
}

func (x *NewsListByCateIdReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *NewsListByCateIdReq) GetIsRecommend() uint32 {
	if x != nil {
		return x.IsRecommend
	}
	return 0
}

type NewsListByCateIdResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ArticleInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NewsListByCateIdResData) Reset() {
	*x = NewsListByCateIdResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsListByCateIdResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByCateIdResData) ProtoMessage() {}

func (x *NewsListByCateIdResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByCateIdResData.ProtoReflect.Descriptor instead.
func (*NewsListByCateIdResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{5}
}

func (x *NewsListByCateIdResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsListByCateIdRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *NewsListByCateIdResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *NewsListByCateIdRes) Reset() {
	*x = NewsListByCateIdRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsListByCateIdRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByCateIdRes) ProtoMessage() {}

func (x *NewsListByCateIdRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByCateIdRes.ProtoReflect.Descriptor instead.
func (*NewsListByCateIdRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{6}
}

func (x *NewsListByCateIdRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsListByCateIdRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsListByCateIdRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsListByCateIdRes) GetData() *NewsListByCateIdResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsHotListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
	IsHot      uint32 `protobuf:"varint,2,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty" dc:"是否热门3条数据 0-否,1-是"`    // 是否热门3条数据 0-否,1-是
}

func (x *NewsHotListReq) Reset() {
	*x = NewsHotListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsHotListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsHotListReq) ProtoMessage() {}

func (x *NewsHotListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsHotListReq.ProtoReflect.Descriptor instead.
func (*NewsHotListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{7}
}

func (x *NewsHotListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *NewsHotListReq) GetIsHot() uint32 {
	if x != nil {
		return x.IsHot
	}
	return 0
}

type NewsHotListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ArticleInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NewsHotListResData) Reset() {
	*x = NewsHotListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsHotListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsHotListResData) ProtoMessage() {}

func (x *NewsHotListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsHotListResData.ProtoReflect.Descriptor instead.
func (*NewsHotListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{8}
}

func (x *NewsHotListResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsHotListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *NewsHotListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *NewsHotListRes) Reset() {
	*x = NewsHotListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsHotListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsHotListRes) ProtoMessage() {}

func (x *NewsHotListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsHotListRes.ProtoReflect.Descriptor instead.
func (*NewsHotListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{9}
}

func (x *NewsHotListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsHotListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsHotListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsHotListRes) GetData() *NewsHotListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId  int32  `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
	LanguageId uint32 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
}

func (x *NewsInfoReq) Reset() {
	*x = NewsInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsInfoReq) ProtoMessage() {}

func (x *NewsInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsInfoReq.ProtoReflect.Descriptor instead.
func (*NewsInfoReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{10}
}

func (x *NewsInfoReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *NewsInfoReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

type NewsInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *ArticleInfo  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *NewsInfoRes) Reset() {
	*x = NewsInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsInfoRes) ProtoMessage() {}

func (x *NewsInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsInfoRes.ProtoReflect.Descriptor instead.
func (*NewsInfoRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{11}
}

func (x *NewsInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsInfoRes) GetData() *ArticleInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
}

func (x *NewsCollectReq) Reset() {
	*x = NewsCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectReq) ProtoMessage() {}

func (x *NewsCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectReq.ProtoReflect.Descriptor instead.
func (*NewsCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{12}
}

func (x *NewsCollectReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

type NewsCollectResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ArticleInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NewsCollectResData) Reset() {
	*x = NewsCollectResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCollectResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectResData) ProtoMessage() {}

func (x *NewsCollectResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectResData.ProtoReflect.Descriptor instead.
func (*NewsCollectResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{13}
}

func (x *NewsCollectResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsCollectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *NewsCollectResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *NewsCollectRes) Reset() {
	*x = NewsCollectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectRes) ProtoMessage() {}

func (x *NewsCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectRes.ProtoReflect.Descriptor instead.
func (*NewsCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{14}
}

func (x *NewsCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsCollectRes) GetData() *NewsCollectResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsCollectStatusCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
}

func (x *NewsCollectStatusCheckReq) Reset() {
	*x = NewsCollectStatusCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCollectStatusCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectStatusCheckReq) ProtoMessage() {}

func (x *NewsCollectStatusCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectStatusCheckReq.ProtoReflect.Descriptor instead.
func (*NewsCollectStatusCheckReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{15}
}

func (x *NewsCollectStatusCheckReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsCollectStatusCheckRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg       string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error     *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	IsCollect int32         `protobuf:"varint,4,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
}

func (x *NewsCollectStatusCheckRes) Reset() {
	*x = NewsCollectStatusCheckRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCollectStatusCheckRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectStatusCheckRes) ProtoMessage() {}

func (x *NewsCollectStatusCheckRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectStatusCheckRes.ProtoReflect.Descriptor instead.
func (*NewsCollectStatusCheckRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{16}
}

func (x *NewsCollectStatusCheckRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCollectStatusCheckRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCollectStatusCheckRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsCollectStatusCheckRes) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type NewsCollectOpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
}

func (x *NewsCollectOpReq) Reset() {
	*x = NewsCollectOpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCollectOpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectOpReq) ProtoMessage() {}

func (x *NewsCollectOpReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectOpReq.ProtoReflect.Descriptor instead.
func (*NewsCollectOpReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{17}
}

func (x *NewsCollectOpReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsCollectOpRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *NewsCollectOpRes) Reset() {
	*x = NewsCollectOpRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsCollectOpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectOpRes) ProtoMessage() {}

func (x *NewsCollectOpRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectOpRes.ProtoReflect.Descriptor instead.
func (*NewsCollectOpRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{18}
}

func (x *NewsCollectOpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCollectOpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCollectOpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type NewsShareOpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
}

func (x *NewsShareOpReq) Reset() {
	*x = NewsShareOpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsShareOpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsShareOpReq) ProtoMessage() {}

func (x *NewsShareOpReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsShareOpReq.ProtoReflect.Descriptor instead.
func (*NewsShareOpReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{19}
}

func (x *NewsShareOpReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsShareOpRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *NewsShareOpRes) Reset() {
	*x = NewsShareOpRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsShareOpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsShareOpRes) ProtoMessage() {}

func (x *NewsShareOpRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsShareOpRes.ProtoReflect.Descriptor instead.
func (*NewsShareOpRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{20}
}

func (x *NewsShareOpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsShareOpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsShareOpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type NewsViewOpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
}

func (x *NewsViewOpReq) Reset() {
	*x = NewsViewOpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsViewOpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsViewOpReq) ProtoMessage() {}

func (x *NewsViewOpReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsViewOpReq.ProtoReflect.Descriptor instead.
func (*NewsViewOpReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{21}
}

func (x *NewsViewOpReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsViewOpRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *NewsViewOpRes) Reset() {
	*x = NewsViewOpRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsViewOpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsViewOpRes) ProtoMessage() {}

func (x *NewsViewOpRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsViewOpRes.ProtoReflect.Descriptor instead.
func (*NewsViewOpRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{22}
}

func (x *NewsViewOpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsViewOpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsViewOpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type NewsTopicListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageId uint32 `protobuf:"varint,1,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
}

func (x *NewsTopicListReq) Reset() {
	*x = NewsTopicListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsTopicListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicListReq) ProtoMessage() {}

func (x *NewsTopicListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicListReq.ProtoReflect.Descriptor instead.
func (*NewsTopicListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{23}
}

func (x *NewsTopicListReq) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

type TopicInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopicId    uint32 `protobuf:"varint,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty" dc:"用户id"`          // 用户id
	LanguageId uint32 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"用户id"` // 用户id
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"账号"`                                  // 账号
	ShortName  string `protobuf:"bytes,4,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty" dc:"账号"`       // 账号
	TopicImgs  string `protobuf:"bytes,5,opt,name=topic_imgs,json=topicImgs,proto3" json:"topic_imgs,omitempty" dc:"专题图片"`     // 专题图片
}

func (x *TopicInfo) Reset() {
	*x = TopicInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicInfo) ProtoMessage() {}

func (x *TopicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicInfo.ProtoReflect.Descriptor instead.
func (*TopicInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{24}
}

func (x *TopicInfo) GetTopicId() uint32 {
	if x != nil {
		return x.TopicId
	}
	return 0
}

func (x *TopicInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *TopicInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TopicInfo) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *TopicInfo) GetTopicImgs() string {
	if x != nil {
		return x.TopicImgs
	}
	return ""
}

type NewsTopicListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TopicInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NewsTopicListResData) Reset() {
	*x = NewsTopicListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsTopicListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicListResData) ProtoMessage() {}

func (x *NewsTopicListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicListResData.ProtoReflect.Descriptor instead.
func (*NewsTopicListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{25}
}

func (x *NewsTopicListResData) GetList() []*TopicInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsTopicListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *NewsTopicListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty" dc:"专题信息"` // 专题信息
}

func (x *NewsTopicListRes) Reset() {
	*x = NewsTopicListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsTopicListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicListRes) ProtoMessage() {}

func (x *NewsTopicListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicListRes.ProtoReflect.Descriptor instead.
func (*NewsTopicListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{26}
}

func (x *NewsTopicListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsTopicListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsTopicListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsTopicListRes) GetData() *NewsTopicListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsListByTopicIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopicId    string `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty" dc:"话题id"`          // 话题id
	LanguageId string `protobuf:"bytes,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"` // 语言id
}

func (x *NewsListByTopicIdReq) Reset() {
	*x = NewsListByTopicIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsListByTopicIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByTopicIdReq) ProtoMessage() {}

func (x *NewsListByTopicIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByTopicIdReq.ProtoReflect.Descriptor instead.
func (*NewsListByTopicIdReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{27}
}

func (x *NewsListByTopicIdReq) GetTopicId() string {
	if x != nil {
		return x.TopicId
	}
	return ""
}

func (x *NewsListByTopicIdReq) GetLanguageId() string {
	if x != nil {
		return x.LanguageId
	}
	return ""
}

type ArticleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArticleId        uint32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty" dc:"文章id"`                         // 文章id
	LanguageId       uint32 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`                      // 语言id
	Name             string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"文章标题"`                                                     // 文章标题
	Content          string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty" dc:"文章内容"`                                               // 文章内容
	CategoryId       uint32 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类id"`                      // 分类id
	CategoryName     string `protobuf:"bytes,6,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty" dc:"分类名称"`                 // 分类名称
	CoverImgs        string `protobuf:"bytes,7,opt,name=cover_imgs,json=coverImgs,proto3" json:"cover_imgs,omitempty" dc:"专题图片"`                          // 专题图片
	Author           string `protobuf:"bytes,8,opt,name=author,proto3" json:"author,omitempty" dc:"创建人"`                                                  // 创建人
	PublishTime      int64  `protobuf:"varint,9,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty" dc:"发布时间"`                   // 发布时间
	AuthorLogo       string `protobuf:"bytes,10,opt,name=author_logo,json=authorLogo,proto3" json:"author_logo,omitempty" dc:"发布时间"`                      // 发布时间
	AuthorAuthStatus uint32 `protobuf:"varint,11,opt,name=author_auth_status,json=authorAuthStatus,proto3" json:"author_auth_status,omitempty" dc:"发布时间"` // 发布时间
}

func (x *ArticleInfo) Reset() {
	*x = ArticleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleInfo) ProtoMessage() {}

func (x *ArticleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleInfo.ProtoReflect.Descriptor instead.
func (*ArticleInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{28}
}

func (x *ArticleInfo) GetArticleId() uint32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *ArticleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ArticleInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ArticleInfo) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ArticleInfo) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *ArticleInfo) GetCoverImgs() string {
	if x != nil {
		return x.CoverImgs
	}
	return ""
}

func (x *ArticleInfo) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ArticleInfo) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *ArticleInfo) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *ArticleInfo) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

type NewsListByTopicIdResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ArticleInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NewsListByTopicIdResData) Reset() {
	*x = NewsListByTopicIdResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsListByTopicIdResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByTopicIdResData) ProtoMessage() {}

func (x *NewsListByTopicIdResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByTopicIdResData.ProtoReflect.Descriptor instead.
func (*NewsListByTopicIdResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{29}
}

func (x *NewsListByTopicIdResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsListByTopicIdRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg       string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error     *common.Error             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	TopicName string                    `protobuf:"bytes,4,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	Data      *NewsListByTopicIdResData `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *NewsListByTopicIdRes) Reset() {
	*x = NewsListByTopicIdRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_news_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsListByTopicIdRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByTopicIdRes) ProtoMessage() {}

func (x *NewsListByTopicIdRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByTopicIdRes.ProtoReflect.Descriptor instead.
func (*NewsListByTopicIdRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{30}
}

func (x *NewsListByTopicIdRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsListByTopicIdRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsListByTopicIdRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsListByTopicIdRes) GetTopicName() string {
	if x != nil {
		return x.TopicName
	}
	return ""
}

func (x *NewsListByTopicIdRes) GetData() *NewsListByTopicIdResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_news_proto protoreflect.FileDescriptor

var file_islamic_v1_news_proto_rawDesc = []byte{
	0x0a, 0x15, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x65, 0x77,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x6e, 0x65,
	0x77, 0x73, 0x5f, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x24, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x6e,
	0x65, 0x77, 0x73, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x70, 0x62, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x48, 0x0a, 0x13, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a,
	0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64,
	0x22, 0x8f, 0x01, 0x0a, 0x0c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x67,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d,
	0x67, 0x73, 0x22, 0x47, 0x0a, 0x17, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x99, 0x01, 0x0a, 0x13,
	0x4e, 0x65, 0x77, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x37,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x72, 0x0a, 0x13, 0x4e, 0x65, 0x77, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x63, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x22, 0x46, 0x0a, 0x17, 0x4e,
	0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x99, 0x01, 0x0a, 0x13, 0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x48, 0x0a, 0x0e, 0x4e, 0x65, 0x77, 0x73, 0x48, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x68, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x69, 0x73, 0x48, 0x6f, 0x74, 0x22, 0x41, 0x0a, 0x12, 0x4e, 0x65, 0x77,
	0x73, 0x48, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8f, 0x01, 0x0a,
	0x0e, 0x4e, 0x65, 0x77, 0x73, 0x48, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x48, 0x6f, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4d,
	0x0a, 0x0b, 0x4e, 0x65, 0x77, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x85, 0x01,
	0x0a, 0x0b, 0x4e, 0x65, 0x77, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x31, 0x0a, 0x0e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x12, 0x4e, 0x65, 0x77, 0x73,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8f, 0x01, 0x0a, 0x0e,
	0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3a, 0x0a,
	0x19, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x19, 0x4e, 0x65,
	0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x22, 0x31, 0x0a, 0x10, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x4f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x10, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x4f, 0x70, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x22, 0x2f, 0x0a, 0x0e, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x4f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x0e, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x4f, 0x70, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x22, 0x2e, 0x0a, 0x0d, 0x4e, 0x65, 0x77, 0x73, 0x56, 0x69, 0x65, 0x77, 0x4f, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0x5a, 0x0a, 0x0d, 0x4e, 0x65, 0x77, 0x73, 0x56, 0x69, 0x65, 0x77, 0x4f, 0x70, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x33, 0x0a,
	0x10, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x22, 0x99, 0x01, 0x0a, 0x09, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x69, 0x6d, 0x67, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x6d, 0x67, 0x73, 0x22, 0x41,
	0x0a, 0x14, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x93, 0x01, 0x0a, 0x10, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77,
	0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x52, 0x0a, 0x14, 0x4e, 0x65, 0x77, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0xea, 0x02, 0x0a, 0x0b,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x67, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x41, 0x75,
	0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x47, 0x0a, 0x18, 0x4e, 0x65, 0x77, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xba, 0x01, 0x0a, 0x14, 0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0xef,
	0x06, 0x0a, 0x0b, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x54,
	0x0a, 0x10, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x4e, 0x65, 0x77, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x10, 0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x43, 0x61, 0x74, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x4e, 0x65,
	0x77, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x11, 0x4e, 0x65, 0x77, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x20, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x20,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x12, 0x3c, 0x0a, 0x08, 0x4e, 0x65, 0x77, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x45,
	0x0a, 0x0b, 0x4e, 0x65, 0x77, 0x73, 0x48, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x48,
	0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x48, 0x6f, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73,
	0x12, 0x66, 0x0a, 0x16, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x25, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x25, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x4e, 0x65, 0x77, 0x73,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x70, 0x12, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x4f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x4f, 0x70, 0x52, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x4f, 0x70, 0x12, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4f, 0x70, 0x52, 0x65, 0x71,
	0x1a, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65,
	0x77, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4f, 0x70, 0x52, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a,
	0x4e, 0x65, 0x77, 0x73, 0x56, 0x69, 0x65, 0x77, 0x4f, 0x70, 0x12, 0x19, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x56, 0x69, 0x65, 0x77,
	0x4f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x73, 0x56, 0x69, 0x65, 0x77, 0x4f, 0x70, 0x52, 0x65, 0x73,
	0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_news_proto_rawDescOnce sync.Once
	file_islamic_v1_news_proto_rawDescData = file_islamic_v1_news_proto_rawDesc
)

func file_islamic_v1_news_proto_rawDescGZIP() []byte {
	file_islamic_v1_news_proto_rawDescOnce.Do(func() {
		file_islamic_v1_news_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_news_proto_rawDescData)
	})
	return file_islamic_v1_news_proto_rawDescData
}

var file_islamic_v1_news_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_islamic_v1_news_proto_goTypes = []interface{}{
	(*NewsCategoryListReq)(nil),       // 0: islamic.v1.NewsCategoryListReq
	(*CategoryInfo)(nil),              // 1: islamic.v1.CategoryInfo
	(*NewsCategoryListResData)(nil),   // 2: islamic.v1.NewsCategoryListResData
	(*NewsCategoryListRes)(nil),       // 3: islamic.v1.NewsCategoryListRes
	(*NewsListByCateIdReq)(nil),       // 4: islamic.v1.NewsListByCateIdReq
	(*NewsListByCateIdResData)(nil),   // 5: islamic.v1.NewsListByCateIdResData
	(*NewsListByCateIdRes)(nil),       // 6: islamic.v1.NewsListByCateIdRes
	(*NewsHotListReq)(nil),            // 7: islamic.v1.NewsHotListReq
	(*NewsHotListResData)(nil),        // 8: islamic.v1.NewsHotListResData
	(*NewsHotListRes)(nil),            // 9: islamic.v1.NewsHotListRes
	(*NewsInfoReq)(nil),               // 10: islamic.v1.NewsInfoReq
	(*NewsInfoRes)(nil),               // 11: islamic.v1.NewsInfoRes
	(*NewsCollectReq)(nil),            // 12: islamic.v1.NewsCollectReq
	(*NewsCollectResData)(nil),        // 13: islamic.v1.NewsCollectResData
	(*NewsCollectRes)(nil),            // 14: islamic.v1.NewsCollectRes
	(*NewsCollectStatusCheckReq)(nil), // 15: islamic.v1.NewsCollectStatusCheckReq
	(*NewsCollectStatusCheckRes)(nil), // 16: islamic.v1.NewsCollectStatusCheckRes
	(*NewsCollectOpReq)(nil),          // 17: islamic.v1.NewsCollectOpReq
	(*NewsCollectOpRes)(nil),          // 18: islamic.v1.NewsCollectOpRes
	(*NewsShareOpReq)(nil),            // 19: islamic.v1.NewsShareOpReq
	(*NewsShareOpRes)(nil),            // 20: islamic.v1.NewsShareOpRes
	(*NewsViewOpReq)(nil),             // 21: islamic.v1.NewsViewOpReq
	(*NewsViewOpRes)(nil),             // 22: islamic.v1.NewsViewOpRes
	(*NewsTopicListReq)(nil),          // 23: islamic.v1.NewsTopicListReq
	(*TopicInfo)(nil),                 // 24: islamic.v1.TopicInfo
	(*NewsTopicListResData)(nil),      // 25: islamic.v1.NewsTopicListResData
	(*NewsTopicListRes)(nil),          // 26: islamic.v1.NewsTopicListRes
	(*NewsListByTopicIdReq)(nil),      // 27: islamic.v1.NewsListByTopicIdReq
	(*ArticleInfo)(nil),               // 28: islamic.v1.ArticleInfo
	(*NewsListByTopicIdResData)(nil),  // 29: islamic.v1.NewsListByTopicIdResData
	(*NewsListByTopicIdRes)(nil),      // 30: islamic.v1.NewsListByTopicIdRes
	(*common.Error)(nil),              // 31: common.Error
}
var file_islamic_v1_news_proto_depIdxs = []int32{
	1,  // 0: islamic.v1.NewsCategoryListResData.list:type_name -> islamic.v1.CategoryInfo
	31, // 1: islamic.v1.NewsCategoryListRes.error:type_name -> common.Error
	2,  // 2: islamic.v1.NewsCategoryListRes.data:type_name -> islamic.v1.NewsCategoryListResData
	28, // 3: islamic.v1.NewsListByCateIdResData.list:type_name -> islamic.v1.ArticleInfo
	31, // 4: islamic.v1.NewsListByCateIdRes.error:type_name -> common.Error
	5,  // 5: islamic.v1.NewsListByCateIdRes.data:type_name -> islamic.v1.NewsListByCateIdResData
	28, // 6: islamic.v1.NewsHotListResData.list:type_name -> islamic.v1.ArticleInfo
	31, // 7: islamic.v1.NewsHotListRes.error:type_name -> common.Error
	8,  // 8: islamic.v1.NewsHotListRes.data:type_name -> islamic.v1.NewsHotListResData
	31, // 9: islamic.v1.NewsInfoRes.error:type_name -> common.Error
	28, // 10: islamic.v1.NewsInfoRes.data:type_name -> islamic.v1.ArticleInfo
	28, // 11: islamic.v1.NewsCollectResData.list:type_name -> islamic.v1.ArticleInfo
	31, // 12: islamic.v1.NewsCollectRes.error:type_name -> common.Error
	13, // 13: islamic.v1.NewsCollectRes.data:type_name -> islamic.v1.NewsCollectResData
	31, // 14: islamic.v1.NewsCollectStatusCheckRes.error:type_name -> common.Error
	31, // 15: islamic.v1.NewsCollectOpRes.error:type_name -> common.Error
	31, // 16: islamic.v1.NewsShareOpRes.error:type_name -> common.Error
	31, // 17: islamic.v1.NewsViewOpRes.error:type_name -> common.Error
	24, // 18: islamic.v1.NewsTopicListResData.list:type_name -> islamic.v1.TopicInfo
	31, // 19: islamic.v1.NewsTopicListRes.error:type_name -> common.Error
	25, // 20: islamic.v1.NewsTopicListRes.data:type_name -> islamic.v1.NewsTopicListResData
	28, // 21: islamic.v1.NewsListByTopicIdResData.list:type_name -> islamic.v1.ArticleInfo
	31, // 22: islamic.v1.NewsListByTopicIdRes.error:type_name -> common.Error
	29, // 23: islamic.v1.NewsListByTopicIdRes.data:type_name -> islamic.v1.NewsListByTopicIdResData
	0,  // 24: islamic.v1.NewsService.NewsCategoryList:input_type -> islamic.v1.NewsCategoryListReq
	4,  // 25: islamic.v1.NewsService.NewsListByCateId:input_type -> islamic.v1.NewsListByCateIdReq
	23, // 26: islamic.v1.NewsService.NewsTopicList:input_type -> islamic.v1.NewsTopicListReq
	27, // 27: islamic.v1.NewsService.NewsListByTopicId:input_type -> islamic.v1.NewsListByTopicIdReq
	10, // 28: islamic.v1.NewsService.NewsInfo:input_type -> islamic.v1.NewsInfoReq
	7,  // 29: islamic.v1.NewsService.NewsHotList:input_type -> islamic.v1.NewsHotListReq
	12, // 30: islamic.v1.NewsService.NewsCollectList:input_type -> islamic.v1.NewsCollectReq
	15, // 31: islamic.v1.NewsService.NewsCollectStatusCheck:input_type -> islamic.v1.NewsCollectStatusCheckReq
	17, // 32: islamic.v1.NewsService.NewsCollectOp:input_type -> islamic.v1.NewsCollectOpReq
	19, // 33: islamic.v1.NewsService.NewsShareOp:input_type -> islamic.v1.NewsShareOpReq
	21, // 34: islamic.v1.NewsService.NewsViewOp:input_type -> islamic.v1.NewsViewOpReq
	3,  // 35: islamic.v1.NewsService.NewsCategoryList:output_type -> islamic.v1.NewsCategoryListRes
	6,  // 36: islamic.v1.NewsService.NewsListByCateId:output_type -> islamic.v1.NewsListByCateIdRes
	26, // 37: islamic.v1.NewsService.NewsTopicList:output_type -> islamic.v1.NewsTopicListRes
	30, // 38: islamic.v1.NewsService.NewsListByTopicId:output_type -> islamic.v1.NewsListByTopicIdRes
	11, // 39: islamic.v1.NewsService.NewsInfo:output_type -> islamic.v1.NewsInfoRes
	9,  // 40: islamic.v1.NewsService.NewsHotList:output_type -> islamic.v1.NewsHotListRes
	14, // 41: islamic.v1.NewsService.NewsCollectList:output_type -> islamic.v1.NewsCollectRes
	16, // 42: islamic.v1.NewsService.NewsCollectStatusCheck:output_type -> islamic.v1.NewsCollectStatusCheckRes
	18, // 43: islamic.v1.NewsService.NewsCollectOp:output_type -> islamic.v1.NewsCollectOpRes
	20, // 44: islamic.v1.NewsService.NewsShareOp:output_type -> islamic.v1.NewsShareOpRes
	22, // 45: islamic.v1.NewsService.NewsViewOp:output_type -> islamic.v1.NewsViewOpRes
	35, // [35:46] is the sub-list for method output_type
	24, // [24:35] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_islamic_v1_news_proto_init() }
func file_islamic_v1_news_proto_init() {
	if File_islamic_v1_news_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_news_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCategoryListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCategoryListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCategoryListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsListByCateIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsListByCateIdResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsListByCateIdRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsHotListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsHotListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsHotListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCollectResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCollectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCollectStatusCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCollectStatusCheckRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCollectOpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsCollectOpRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsShareOpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsShareOpRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsViewOpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsViewOpRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsTopicListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsTopicListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsTopicListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsListByTopicIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsListByTopicIdResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_news_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsListByTopicIdRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_news_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_news_proto_goTypes,
		DependencyIndexes: file_islamic_v1_news_proto_depIdxs,
		MessageInfos:      file_islamic_v1_news_proto_msgTypes,
	}.Build()
	File_islamic_v1_news_proto = out.File
	file_islamic_v1_news_proto_rawDesc = nil
	file_islamic_v1_news_proto_goTypes = nil
	file_islamic_v1_news_proto_depIdxs = nil
}
