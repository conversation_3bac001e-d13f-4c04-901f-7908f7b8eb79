// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/surah.proto

package islamicv1

import (
	common "halalplus/api/common"
	pbentity "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 古兰经-章-列表
type SurahListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"章节id"`                                // 章节id
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`                               //名称
	IsPopular uint32 `protobuf:"varint,3,opt,name=is_popular,json=isPopular,proto3" json:"is_popular,omitempty" dc:"是否热门"` // 是否热门
}

func (x *SurahListReq) Reset() {
	*x = SurahListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListReq) ProtoMessage() {}

func (x *SurahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListReq.ProtoReflect.Descriptor instead.
func (*SurahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{0}
}

func (x *SurahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurahListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurahListReq) GetIsPopular() uint32 {
	if x != nil {
		return x.IsPopular
	}
	return 0
}

type SurahListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*pbentity.SuratDaftar `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SurahListResData) Reset() {
	*x = SurahListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListResData) ProtoMessage() {}

func (x *SurahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListResData.ProtoReflect.Descriptor instead.
func (*SurahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{1}
}

func (x *SurahListResData) GetList() []*pbentity.SuratDaftar {
	if x != nil {
		return x.List
	}
	return nil
}

type SurahListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error     `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *SurahListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SurahListRes) Reset() {
	*x = SurahListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListRes) ProtoMessage() {}

func (x *SurahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListRes.ProtoReflect.Descriptor instead.
func (*SurahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{2}
}

func (x *SurahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahListRes) GetData() *SurahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type JuzListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" dc:"juz名称"` // juz名称
}

func (x *JuzListReq) Reset() {
	*x = JuzListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListReq) ProtoMessage() {}

func (x *JuzListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListReq.ProtoReflect.Descriptor instead.
func (*JuzListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{3}
}

func (x *JuzListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type JuzInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartSurahId   uint32 `protobuf:"varint,1,opt,name=start_surah_id,json=startSurahId,proto3" json:"start_surah_id,omitempty" dc:"开始章id"`        // 开始章id
	StartSurahName string `protobuf:"bytes,2,opt,name=start_surah_name,json=startSurahName,proto3" json:"start_surah_name,omitempty" dc:"开始章name"` // 开始章name
	EndSurahId     uint32 `protobuf:"varint,3,opt,name=end_surah_id,json=endSurahId,proto3" json:"end_surah_id,omitempty" dc:"结束章id"`              // 结束章id
	EndSurahName   string `protobuf:"bytes,4,opt,name=end_surah_name,json=endSurahName,proto3" json:"end_surah_name,omitempty" dc:"结束章name"`       // 结束章name
	StartAyahId    uint32 `protobuf:"varint,5,opt,name=start_ayah_id,json=startAyahId,proto3" json:"start_ayah_id,omitempty" dc:"开始节id"`           // 开始节id
	EndAyahId      uint32 `protobuf:"varint,6,opt,name=end_ayah_id,json=endAyahId,proto3" json:"end_ayah_id,omitempty" dc:"结束节id"`                 // 结束节id
	Juz            string `protobuf:"bytes,7,opt,name=juz,proto3" json:"juz,omitempty" dc:"juz名称"`                                                 // juz名称
	FirstWord      string `protobuf:"bytes,8,opt,name=first_word,json=firstWord,proto3" json:"first_word,omitempty" dc:"对应经文的第一个单词"`               // 对应经文的第一个单词
}

func (x *JuzInfo) Reset() {
	*x = JuzInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzInfo) ProtoMessage() {}

func (x *JuzInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzInfo.ProtoReflect.Descriptor instead.
func (*JuzInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{4}
}

func (x *JuzInfo) GetStartSurahId() uint32 {
	if x != nil {
		return x.StartSurahId
	}
	return 0
}

func (x *JuzInfo) GetStartSurahName() string {
	if x != nil {
		return x.StartSurahName
	}
	return ""
}

func (x *JuzInfo) GetEndSurahId() uint32 {
	if x != nil {
		return x.EndSurahId
	}
	return 0
}

func (x *JuzInfo) GetEndSurahName() string {
	if x != nil {
		return x.EndSurahName
	}
	return ""
}

func (x *JuzInfo) GetStartAyahId() uint32 {
	if x != nil {
		return x.StartAyahId
	}
	return 0
}

func (x *JuzInfo) GetEndAyahId() uint32 {
	if x != nil {
		return x.EndAyahId
	}
	return 0
}

func (x *JuzInfo) GetJuz() string {
	if x != nil {
		return x.Juz
	}
	return ""
}

func (x *JuzInfo) GetFirstWord() string {
	if x != nil {
		return x.FirstWord
	}
	return ""
}

type JuzListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*JuzInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *JuzListResData) Reset() {
	*x = JuzListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListResData) ProtoMessage() {}

func (x *JuzListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListResData.ProtoReflect.Descriptor instead.
func (*JuzListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{5}
}

func (x *JuzListResData) GetList() []*JuzInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type JuzListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *JuzListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *JuzListRes) Reset() {
	*x = JuzListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListRes) ProtoMessage() {}

func (x *JuzListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListRes.ProtoReflect.Descriptor instead.
func (*JuzListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{6}
}

func (x *JuzListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JuzListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *JuzListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *JuzListRes) GetData() *JuzListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type AyahListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`                           // 节id
	SurahId uint32 `protobuf:"varint,2,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章节id"` //章节id
	JuzId   uint32 `protobuf:"varint,3,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz_id"`     //juz_id
	Page    uint32 `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty" dc:"page 页数量"`                  //page 页数量
}

func (x *AyahListReq) Reset() {
	*x = AyahListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListReq) ProtoMessage() {}

func (x *AyahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListReq.ProtoReflect.Descriptor instead.
func (*AyahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{7}
}

func (x *AyahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AyahListReq) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *AyahListReq) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

func (x *AyahListReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type AyahListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*pbentity.SuratAyat `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AyahListResData) Reset() {
	*x = AyahListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListResData) ProtoMessage() {}

func (x *AyahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListResData.ProtoReflect.Descriptor instead.
func (*AyahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{8}
}

func (x *AyahListResData) GetList() []*pbentity.SuratAyat {
	if x != nil {
		return x.List
	}
	return nil
}

type AyahListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahListRes) Reset() {
	*x = AyahListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListRes) ProtoMessage() {}

func (x *AyahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListRes.ProtoReflect.Descriptor instead.
func (*AyahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{9}
}

func (x *AyahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahListRes) GetData() *AyahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId   uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`                    //节id
	IsUserOp uint32 `protobuf:"varint,2,opt,name=is_user_op,json=isUserOp,proto3" json:"is_user_op,omitempty" dc:"是否用户操作，1-是，0-否"` //是否用户操作，1-是，0-否
}

func (x *AyahReadRecordReq) Reset() {
	*x = AyahReadRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordReq) ProtoMessage() {}

func (x *AyahReadRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{10}
}

func (x *AyahReadRecordReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadRecordReq) GetIsUserOp() uint32 {
	if x != nil {
		return x.IsUserOp
	}
	return 0
}

type AyahReadRecordRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AyahReadRecordRes) Reset() {
	*x = AyahReadRecordRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordRes) ProtoMessage() {}

func (x *AyahReadRecordRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{11}
}

func (x *AyahReadRecordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type AyahReadCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId uint32 `protobuf:"varint,2,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"`            //章节id
	IsAdd  uint32 `protobuf:"varint,3,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` //是否添加收藏，1-添加，0-取消收藏
}

func (x *AyahReadCollectReq) Reset() {
	*x = AyahReadCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectReq) ProtoMessage() {}

func (x *AyahReadCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{12}
}

func (x *AyahReadCollectReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

type AyahReadCollectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AyahReadCollectRes) Reset() {
	*x = AyahReadCollectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectRes) ProtoMessage() {}

func (x *AyahReadCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{13}
}

func (x *AyahReadCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckAyahReadCollectStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"` //章节id
}

func (x *CheckAyahReadCollectStatusReq) Reset() {
	*x = CheckAyahReadCollectStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusReq) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{14}
}

func (x *CheckAyahReadCollectStatusReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

type CheckAyahReadCollectStatusRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg       string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error     *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	IsCollect int32         `protobuf:"varint,4,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
}

func (x *CheckAyahReadCollectStatusRes) Reset() {
	*x = CheckAyahReadCollectStatusRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusRes) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{15}
}

func (x *CheckAyahReadCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckAyahReadCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckAyahReadCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckAyahReadCollectStatusRes) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type ReadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurahId   uint32 `protobuf:"varint,1,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章id"`        // 章id
	SurahName string `protobuf:"bytes,2,opt,name=surah_name,json=surahName,proto3" json:"surah_name,omitempty" dc:"章name"` // 章name
	AyahId    uint32 `protobuf:"varint,3,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`           // 节id
	JuzId     uint32 `protobuf:"varint,4,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz-id"`           // juz-id
}

func (x *ReadInfo) Reset() {
	*x = ReadInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadInfo) ProtoMessage() {}

func (x *ReadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadInfo.ProtoReflect.Descriptor instead.
func (*ReadInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{16}
}

func (x *ReadInfo) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *ReadInfo) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *ReadInfo) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *ReadInfo) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

type AyahReadRecordListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AyahReadRecordListReq) Reset() {
	*x = AyahReadRecordListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListReq) ProtoMessage() {}

func (x *AyahReadRecordListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{17}
}

type AyahReadRecordListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ReadInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AyahReadRecordListResData) Reset() {
	*x = AyahReadRecordListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListResData) ProtoMessage() {}

func (x *AyahReadRecordListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListResData.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{18}
}

func (x *AyahReadRecordListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type AyahReadRecordListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahReadRecordListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahReadRecordListRes) Reset() {
	*x = AyahReadRecordListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListRes) ProtoMessage() {}

func (x *AyahReadRecordListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{19}
}

func (x *AyahReadRecordListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadRecordListRes) GetData() *AyahReadRecordListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadCollectListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AyahReadCollectListReq) Reset() {
	*x = AyahReadCollectListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListReq) ProtoMessage() {}

func (x *AyahReadCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{20}
}

type AyahReadCollectListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ReadInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AyahReadCollectListResData) Reset() {
	*x = AyahReadCollectListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListResData) ProtoMessage() {}

func (x *AyahReadCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListResData.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{21}
}

func (x *AyahReadCollectListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type AyahReadCollectListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahReadCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahReadCollectListRes) Reset() {
	*x = AyahReadCollectListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListRes) ProtoMessage() {}

func (x *AyahReadCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{22}
}

func (x *AyahReadCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadCollectListRes) GetData() *AyahReadCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_surah_proto protoreflect.FileDescriptor

var file_islamic_v1_surah_proto_rawDesc = []byte{
	0x0a, 0x16, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72,
	0x61, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73,
	0x75, 0x72, 0x61, 0x74, 0x5f, 0x61, 0x79, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74, 0x5f,
	0x64, 0x61, 0x66, 0x74, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x62,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74, 0x5f, 0x74, 0x61, 0x66,
	0x73, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x51, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x70, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x69,
	0x73, 0x50, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x22, 0x3d, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x62, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x74, 0x44, 0x61, 0x66, 0x74, 0x61,
	0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8b, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x20, 0x0a, 0x0a, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x07, 0x4a, 0x75, 0x7a, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x72,
	0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x53, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x75, 0x72, 0x61, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x6e, 0x64, 0x53, 0x75,
	0x72, 0x61, 0x68, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x75, 0x72,
	0x61, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x6e, 0x64, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x41, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6a, 0x75, 0x7a, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x75,
	0x7a, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x64,
	0x22, 0x39, 0x0a, 0x0e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x75,
	0x7a, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x87, 0x01, 0x0a, 0x0a,
	0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x63, 0x0a, 0x0b, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x6a, 0x75, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x6a, 0x75, 0x7a, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x3a, 0x0a, 0x0f, 0x41, 0x79,
	0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x62,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x74, 0x41, 0x79, 0x61, 0x74,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x89, 0x01, 0x0a, 0x0b, 0x41, 0x79, 0x61, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x4a, 0x0a, 0x11, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x70, 0x22, 0x5e,
	0x0a, 0x11, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x44,
	0x0a, 0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69,
	0x73, 0x41, 0x64, 0x64, 0x22, 0x5f, 0x0a, 0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x38, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79,
	0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x22,
	0x89, 0x01, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61,
	0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x22, 0x74, 0x0a, 0x08, 0x52,
	0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x72, 0x61, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x75, 0x72, 0x61, 0x68,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x75,
	0x7a, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6a, 0x75, 0x7a, 0x49,
	0x64, 0x22, 0x17, 0x0a, 0x15, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x45, 0x0a, 0x19, 0x41, 0x79,
	0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x9d, 0x01, 0x0a, 0x15, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x18, 0x0a, 0x16, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x46, 0x0a, 0x1a, 0x41,
	0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x9f, 0x01, 0x0a, 0x16, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x9a, 0x05, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x07, 0x4a, 0x75, 0x7a, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x12, 0x4e, 0x0a, 0x0e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x12, 0x5a, 0x0a, 0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x72, 0x0a, 0x1a,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61,
	0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x12, 0x51, 0x0a, 0x0f, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x12, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x13, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68,
	0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_surah_proto_rawDescOnce sync.Once
	file_islamic_v1_surah_proto_rawDescData = file_islamic_v1_surah_proto_rawDesc
)

func file_islamic_v1_surah_proto_rawDescGZIP() []byte {
	file_islamic_v1_surah_proto_rawDescOnce.Do(func() {
		file_islamic_v1_surah_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_surah_proto_rawDescData)
	})
	return file_islamic_v1_surah_proto_rawDescData
}

var file_islamic_v1_surah_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_islamic_v1_surah_proto_goTypes = []interface{}{
	(*SurahListReq)(nil),                  // 0: islamic.v1.SurahListReq
	(*SurahListResData)(nil),              // 1: islamic.v1.SurahListResData
	(*SurahListRes)(nil),                  // 2: islamic.v1.SurahListRes
	(*JuzListReq)(nil),                    // 3: islamic.v1.JuzListReq
	(*JuzInfo)(nil),                       // 4: islamic.v1.JuzInfo
	(*JuzListResData)(nil),                // 5: islamic.v1.JuzListResData
	(*JuzListRes)(nil),                    // 6: islamic.v1.JuzListRes
	(*AyahListReq)(nil),                   // 7: islamic.v1.AyahListReq
	(*AyahListResData)(nil),               // 8: islamic.v1.AyahListResData
	(*AyahListRes)(nil),                   // 9: islamic.v1.AyahListRes
	(*AyahReadRecordReq)(nil),             // 10: islamic.v1.AyahReadRecordReq
	(*AyahReadRecordRes)(nil),             // 11: islamic.v1.AyahReadRecordRes
	(*AyahReadCollectReq)(nil),            // 12: islamic.v1.AyahReadCollectReq
	(*AyahReadCollectRes)(nil),            // 13: islamic.v1.AyahReadCollectRes
	(*CheckAyahReadCollectStatusReq)(nil), // 14: islamic.v1.CheckAyahReadCollectStatusReq
	(*CheckAyahReadCollectStatusRes)(nil), // 15: islamic.v1.CheckAyahReadCollectStatusRes
	(*ReadInfo)(nil),                      // 16: islamic.v1.ReadInfo
	(*AyahReadRecordListReq)(nil),         // 17: islamic.v1.AyahReadRecordListReq
	(*AyahReadRecordListResData)(nil),     // 18: islamic.v1.AyahReadRecordListResData
	(*AyahReadRecordListRes)(nil),         // 19: islamic.v1.AyahReadRecordListRes
	(*AyahReadCollectListReq)(nil),        // 20: islamic.v1.AyahReadCollectListReq
	(*AyahReadCollectListResData)(nil),    // 21: islamic.v1.AyahReadCollectListResData
	(*AyahReadCollectListRes)(nil),        // 22: islamic.v1.AyahReadCollectListRes
	(*pbentity.SuratDaftar)(nil),          // 23: pbentity.SuratDaftar
	(*common.Error)(nil),                  // 24: common.Error
	(*pbentity.SuratAyat)(nil),            // 25: pbentity.SuratAyat
}
var file_islamic_v1_surah_proto_depIdxs = []int32{
	23, // 0: islamic.v1.SurahListResData.list:type_name -> pbentity.SuratDaftar
	24, // 1: islamic.v1.SurahListRes.error:type_name -> common.Error
	1,  // 2: islamic.v1.SurahListRes.data:type_name -> islamic.v1.SurahListResData
	4,  // 3: islamic.v1.JuzListResData.list:type_name -> islamic.v1.JuzInfo
	24, // 4: islamic.v1.JuzListRes.error:type_name -> common.Error
	5,  // 5: islamic.v1.JuzListRes.data:type_name -> islamic.v1.JuzListResData
	25, // 6: islamic.v1.AyahListResData.list:type_name -> pbentity.SuratAyat
	24, // 7: islamic.v1.AyahListRes.error:type_name -> common.Error
	8,  // 8: islamic.v1.AyahListRes.data:type_name -> islamic.v1.AyahListResData
	24, // 9: islamic.v1.AyahReadRecordRes.error:type_name -> common.Error
	24, // 10: islamic.v1.AyahReadCollectRes.error:type_name -> common.Error
	24, // 11: islamic.v1.CheckAyahReadCollectStatusRes.error:type_name -> common.Error
	16, // 12: islamic.v1.AyahReadRecordListResData.list:type_name -> islamic.v1.ReadInfo
	24, // 13: islamic.v1.AyahReadRecordListRes.error:type_name -> common.Error
	18, // 14: islamic.v1.AyahReadRecordListRes.data:type_name -> islamic.v1.AyahReadRecordListResData
	16, // 15: islamic.v1.AyahReadCollectListResData.list:type_name -> islamic.v1.ReadInfo
	24, // 16: islamic.v1.AyahReadCollectListRes.error:type_name -> common.Error
	21, // 17: islamic.v1.AyahReadCollectListRes.data:type_name -> islamic.v1.AyahReadCollectListResData
	0,  // 18: islamic.v1.SurahService.SurahList:input_type -> islamic.v1.SurahListReq
	3,  // 19: islamic.v1.SurahService.JuzList:input_type -> islamic.v1.JuzListReq
	7,  // 20: islamic.v1.SurahService.AyahList:input_type -> islamic.v1.AyahListReq
	10, // 21: islamic.v1.SurahService.AyahReadRecord:input_type -> islamic.v1.AyahReadRecordReq
	17, // 22: islamic.v1.SurahService.AyahReadRecordList:input_type -> islamic.v1.AyahReadRecordListReq
	14, // 23: islamic.v1.SurahService.CheckAyahReadCollectStatus:input_type -> islamic.v1.CheckAyahReadCollectStatusReq
	12, // 24: islamic.v1.SurahService.AyahReadCollect:input_type -> islamic.v1.AyahReadCollectReq
	20, // 25: islamic.v1.SurahService.AyahReadCollectList:input_type -> islamic.v1.AyahReadCollectListReq
	2,  // 26: islamic.v1.SurahService.SurahList:output_type -> islamic.v1.SurahListRes
	6,  // 27: islamic.v1.SurahService.JuzList:output_type -> islamic.v1.JuzListRes
	9,  // 28: islamic.v1.SurahService.AyahList:output_type -> islamic.v1.AyahListRes
	11, // 29: islamic.v1.SurahService.AyahReadRecord:output_type -> islamic.v1.AyahReadRecordRes
	19, // 30: islamic.v1.SurahService.AyahReadRecordList:output_type -> islamic.v1.AyahReadRecordListRes
	15, // 31: islamic.v1.SurahService.CheckAyahReadCollectStatus:output_type -> islamic.v1.CheckAyahReadCollectStatusRes
	13, // 32: islamic.v1.SurahService.AyahReadCollect:output_type -> islamic.v1.AyahReadCollectRes
	22, // 33: islamic.v1.SurahService.AyahReadCollectList:output_type -> islamic.v1.AyahReadCollectListRes
	26, // [26:34] is the sub-list for method output_type
	18, // [18:26] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_islamic_v1_surah_proto_init() }
func file_islamic_v1_surah_proto_init() {
	if File_islamic_v1_surah_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_surah_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_surah_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_surah_proto_goTypes,
		DependencyIndexes: file_islamic_v1_surah_proto_depIdxs,
		MessageInfos:      file_islamic_v1_surah_proto_msgTypes,
	}.Build()
	File_islamic_v1_surah_proto = out.File
	file_islamic_v1_surah_proto_rawDesc = nil
	file_islamic_v1_surah_proto_goTypes = nil
	file_islamic_v1_surah_proto_depIdxs = nil
}
