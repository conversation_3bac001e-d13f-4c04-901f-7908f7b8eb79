package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/api/pbentity"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"sort"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/util/gconv"
)

type Controller struct {
	v1.UnimplementedSurahServiceServer
}

type ControllerNews struct {
	v1.UnimplementedNewsServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterSurahServiceServer(s.Server, &Controller{})
	v1.RegisterNewsServiceServer(s.Server, &ControllerNews{})
	v1.RegisterBannerServiceServer(s.Server, &ControllerBanner{})
	v1.RegisterFaqServiceServer(s.Server, &ControllerFaq{})
	v1.RegisterPrayerServiceServer(s.Server, &ControllerPrayer{})
	v1.RegisterWisdomServiceServer(s.Server, &ControllerWisdom{})
}

func (*Controller) SurahList(ctx context.Context, req *v1.SurahListReq) (res *v1.SurahListRes, err error) {
	res = &v1.SurahListRes{}
	res.Code = 200
	res.Msg = "success"
	surahParamInput := &model.SurahParamInput{
		Id:        gconv.Uint(req.Id),
		Name:      req.Name,
		IsPopular: uint(req.IsPopular),
	}
	surahList := service.Islamic().SurahList(ctx, surahParamInput)

	outList := make([]*pbentity.SuratDaftar, 0, len(surahList)) // 初始化切片，预分配空间
	for _, surah := range surahList {
		outList = append(outList, &pbentity.SuratDaftar{
			Id:          int32(gconv.Uint32(surah.Id)),
			NamaLatin:   surah.NameLatin,
			Nama:        surah.Name,
			JumlahAyat:  int32(surah.JumlahAyat),
			Arti:        surah.Arti,
			Nomor:       int32(surah.Nomor),
			TempatTurun: surah.TempatTurun,
		})
	}
	surahListResData := &v1.SurahListResData{
		List: outList,
	}
	res.Data = surahListResData
	return res, nil
}

func (*Controller) JuzList(ctx context.Context, req *v1.JuzListReq) (res *v1.JuzListRes, err error) {
	res = &v1.JuzListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.JuzParamInput{
		Name: req.Name,
	}
	juzList := service.Islamic().JuzList(ctx, JuzParamInput)

	outList := make([]*v1.JuzInfo, 0, 30)
	for _, juz := range juzList {
		outList = append(outList, &v1.JuzInfo{
			StartSurahId:   uint32(juz.StartSurahId),
			StartSurahName: juz.StartSurahName,
			EndSurahId:     uint32(juz.EndSurahId),
			EndSurahName:   juz.EndSurahName,
			StartAyahId:    uint32(juz.StartAyahId),
			EndAyahId:      uint32(juz.EndAyahId),
			Juz:            juz.Name,
			FirstWord:      juz.FirstWord,
		})
	}
	//Data 排序
	sort.Slice(outList, func(i, j int) bool {
		return outList[i].StartSurahId < outList[j].StartSurahId
	})
	juzListResData := &v1.JuzListResData{
		List: outList,
	}
	res.Data = juzListResData
	return res, nil

}

func (*Controller) AyahList(ctx context.Context, req *v1.AyahListReq) (res *v1.AyahListRes, err error) {

	res = &v1.AyahListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahParamInput{
		Id:      req.Id,
		SurahId: req.SurahId,
		JuzId:   req.JuzId,
		Page:    req.Page,
	}
	juzList := service.Islamic().AyahList(ctx, JuzParamInput)

	outList := make([]*pbentity.SuratAyat, 0, len(juzList)) // 初始化切片，预分配空间
	for _, surah := range juzList {
		outList = append(outList, &pbentity.SuratAyat{
			Id:      int32(surah.Id),
			AyatId:  int32(surah.Id),
			SurahId: int32(surah.SurahId),
			Nomor:   int32(surah.Nomor),
			Ar:      surah.Ar,
			Tr:      surah.Tr,
			Idn:     surah.Idn,
			Page:    int32(surah.Page),
			Juz:     int32(surah.Juz),
		})
	}
	ayahListResData := &v1.AyahListResData{
		List: outList,
	}
	res.Data = ayahListResData
	return res, nil

}

// 阅读记录
func (*Controller) AyahReadRecord(ctx context.Context, req *v1.AyahReadRecordReq) (res *v1.AyahReadRecordRes, err error) {

	res = &v1.AyahReadRecordRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadRecordInput{
		AyahId:   req.AyahId,
		IsUserOp: req.IsUserOp,
	}
	service.Islamic().AyahReadRecord(ctx, JuzParamInput)
	return res, nil

}
func (*Controller) AyahReadRecordList(ctx context.Context, req *v1.AyahReadRecordListReq) (res *v1.AyahReadRecordListRes, err error) {

	res = &v1.AyahReadRecordListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadRecordListInput{}
	juzList := service.Islamic().AyahReadRecordList(ctx, JuzParamInput)

	outList := make([]*v1.ReadInfo, 0, len(juzList)) // 初始化切片，预分配空间
	for _, surah := range juzList {
		outList = append(outList, &v1.ReadInfo{
			SurahId:   uint32(surah.SurahId),
			SurahName: surah.SurahName,
			AyahId:    uint32(surah.AyahId),
			JuzId:     uint32(surah.JuzId),
		})
	}

	ayahReadRecordListResData := &v1.AyahReadRecordListResData{
		List: outList,
	}
	res.Data = ayahReadRecordListResData

	return res, nil

}

// 阅读收藏
func (*Controller) AyahReadCollect(ctx context.Context, req *v1.AyahReadCollectReq) (res *v1.AyahReadCollectRes, err error) {

	res = &v1.AyahReadCollectRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadCollectInput{
		AyahId: req.AyahId,
	}
	service.Islamic().AyahReadCollect(ctx, JuzParamInput)
	return res, nil
}
func (*Controller) CheckAyahReadCollectStatus(ctx context.Context, req *v1.CheckAyahReadCollectStatusReq) (res *v1.CheckAyahReadCollectStatusRes, err error) {

	res = &v1.CheckAyahReadCollectStatusRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.CheckAyahReadCollectStatusInput{
		AyahId: req.AyahId,
	}
	CollectStatus := service.Islamic().CheckAyahReadCollectStatus(ctx, JuzParamInput)
	if CollectStatus != nil {
		res.IsCollect = int32(CollectStatus.IsCollect)
	}
	return res, nil

}

// 阅读记录列表
func (*Controller) AyahReadCollectList(ctx context.Context, req *v1.AyahReadCollectListReq) (res *v1.AyahReadCollectListRes, err error) {

	res = &v1.AyahReadCollectListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadCollectListInput{}
	juzList := service.Islamic().AyahReadCollectList(ctx, JuzParamInput)

	outList := make([]*v1.ReadInfo, 0, len(juzList)) // 初始化切片，预分配空间
	for _, surah := range juzList {
		outList = append(outList, &v1.ReadInfo{
			SurahId:   uint32(surah.SurahId),
			SurahName: surah.SurahName,
			AyahId:    uint32(surah.AyahId),
			JuzId:     uint32(surah.JuzId),
		})
	}

	ayahReadCollectListResData := &v1.AyahReadCollectListResData{
		List: outList,
	}
	res.Data = ayahReadCollectListResData
	return res, nil

}

//func (*Controller) SurahInfo(ctx context.Context, req *v1.SurahInfoReq) (res *v1.SurahInfoRes, err error) {
//
//	res = &v1.SurahInfoRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratDaftar{
//		Id:          1,
//		Nama:        "212323",
//		Nomor:       1,
//		NamaLatin:   "Pembukaan",
//		JumlahAyat:  4,
//		TempatTurun: "Pembukaan",
//		Arti:        "Pembukaan",
//		Deskripsi:   "Pembukaan",
//		Audio:       "https://equran.nos.wjv-1.neo.id/audio-full/Misyari-Rasyid-Al-Afasi/012.mp3",
//		Status:      1,
//	}
//	return res, nil
//}
//
//func (*Controller) SurahDesc(ctx context.Context, req *v1.SurahDescReq) (res *v1.SurahDescRes, err error) {
//
//	res = &v1.SurahDescRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratTafsir{
//		Id:        1,
//		TafsirId:  1,
//		SurahId:   1,
//		AyatNomor: 1,
//		Tafsir:    "Pembukaan",
//	}
//	return res, nil
//}

func (*ControllerNews) NewsCategoryList(ctx context.Context, req *v1.NewsCategoryListReq) (res *v1.NewsCategoryListRes, err error) {

	res = &v1.NewsCategoryListRes{}
	res.Code = 200
	res.Msg = "success"
	ParamInput := &model.NewsCategoryListInput{
		LanguageId: req.LanguageId,
		Pid:        req.Pid,
	}
	CategoryList := service.Islamic().NewsCategoryList(ctx, ParamInput)

	outList := make([]*v1.CategoryInfo, 0, len(CategoryList)) // 初始化切片，预分配空间
	for _, cate := range CategoryList {
		outList = append(outList, &v1.CategoryInfo{
			Id:         cate.Id,
			ParentId:   cate.ParentId,
			LanguageId: cate.LanguageId,
			Name:       cate.Name,
			CoverImgs:  cate.CoverImgs,
		})
	}

	newsCategoryListResData := &v1.NewsCategoryListResData{
		List: outList,
	}
	res.Data = newsCategoryListResData
	return res, nil
}

func (*ControllerNews) NewsListByCateId(ctx context.Context, req *v1.NewsListByCateIdReq) (res *v1.NewsListByCateIdRes, err error) {

	res = &v1.NewsListByCateIdRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsListByCateIdInput{}
	gconv.Struct(req, JuzParamInput)
	rtList := service.Islamic().NewsListByCateId(ctx, JuzParamInput)
	outList := make([]*v1.ArticleInfo, 0, len(rtList)) // 初始化切片，预分配空间
	for _, cate := range rtList {
		Data := &v1.ArticleInfo{}
		gconv.Struct(cate, Data)
		outList = append(outList, Data)
	}

	newsListByCateIdResData := &v1.NewsListByCateIdResData{
		List: outList,
	}
	res.Data = newsListByCateIdResData
	return res, nil
}

func (*ControllerNews) NewsTopicList(ctx context.Context, req *v1.NewsTopicListReq) (res *v1.NewsTopicListRes, err error) {

	res = &v1.NewsTopicListRes{}
	res.Code = 200
	res.Msg = "success"
	ParamInput := &model.NewsTopicListInput{
		LanguageId: req.LanguageId,
	}
	TopicList := service.Islamic().NewsTopicList(ctx, ParamInput)

	outList := make([]*v1.TopicInfo, 0, len(TopicList)) // 初始化切片，预分配空间
	for _, cate := range TopicList {
		outList = append(outList, &v1.TopicInfo{
			TopicId:    cate.Id,
			LanguageId: cate.LanguageId,
			Name:       cate.Name,
			ShortName:  cate.ShortName,
			TopicImgs:  cate.TopicImgs,
		})
	}
	newsTopicListResData := &v1.NewsTopicListResData{
		List: outList,
	}
	res.Data = newsTopicListResData

	return res, nil

}

func (*ControllerNews) NewsListByTopicId(ctx context.Context, req *v1.NewsListByTopicIdReq) (res *v1.NewsListByTopicIdRes, err error) {

	res = &v1.NewsListByTopicIdRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsListByTopicIdInput{}
	gconv.Struct(req, JuzParamInput)
	rtList := service.Islamic().NewsListByTopicId(ctx, JuzParamInput)
	outList := make([]*v1.ArticleInfo, 0, len(rtList)) // 初始化切片，预分配空间
	for _, cate := range rtList {
		res.TopicName = cate.TopicName
		Data := &v1.ArticleInfo{}
		gconv.Struct(cate, Data)
		outList = append(outList, Data)
	}

	newsListByTopicIdResData := &v1.NewsListByTopicIdResData{
		List: outList,
	}
	res.Data = newsListByTopicIdResData
	return res, nil
}

func (*ControllerNews) NewsInfo(ctx context.Context, req *v1.NewsInfoReq) (res *v1.NewsInfoRes, err error) {

	res = &v1.NewsInfoRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsInfoInput{}
	gconv.Struct(req, JuzParamInput)
	rtList := service.Islamic().NewsInfo(ctx, JuzParamInput)
	Data := &v1.ArticleInfo{}
	if len(rtList) == 0 {
		return res, nil
	}
	gconv.Struct(rtList[0], Data)
	res.Data = Data
	return res, nil
}

func (*ControllerNews) NewsHotList(ctx context.Context, req *v1.NewsHotListReq) (res *v1.NewsHotListRes, err error) {

	res = &v1.NewsHotListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsHotListInput{}
	gconv.Struct(req, JuzParamInput)
	rtList := service.Islamic().NewsHotList(ctx, JuzParamInput)
	outList := make([]*v1.ArticleInfo, 0, len(rtList)) // 初始化切片，预分配空间
	for _, cate := range rtList {
		Data := &v1.ArticleInfo{}
		gconv.Struct(cate, Data)
		outList = append(outList, Data)
	}

	newsHotListResData := &v1.NewsHotListResData{
		List: outList,
	}
	res.Data = newsHotListResData
	return res, nil
}

func (*ControllerNews) NewsCollectList(ctx context.Context, req *v1.NewsCollectReq) (res *v1.NewsCollectRes, err error) {

	res = &v1.NewsCollectRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectListInput{}
	gconv.Struct(req, JuzParamInput)
	rtList := service.Islamic().NewsCollectList(ctx, JuzParamInput)
	outList := make([]*v1.ArticleInfo, 0, len(rtList)) // 初始化切片，预分配空间
	for _, cate := range rtList {
		Data := &v1.ArticleInfo{}
		gconv.Struct(cate, Data)
		outList = append(outList, Data)
	}

	newsCollectResData := &v1.NewsCollectResData{
		List: outList,
	}
	res.Data = newsCollectResData
	return res, nil
}

func (*ControllerNews) NewsCollectStatusCheck(ctx context.Context, req *v1.NewsCollectStatusCheckReq) (res *v1.NewsCollectStatusCheckRes, err error) {
	res = &v1.NewsCollectStatusCheckRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectStatusCheckInput{
		ArticleId: uint32(req.ArticleId),
	}
	CollectStatus := service.Islamic().NewsCollectStatusCheck(ctx, JuzParamInput)
	if CollectStatus != nil {
		res.IsCollect = int32(CollectStatus.IsCollect)
	}
	return res, nil
}

func (*ControllerNews) NewsCollectOp(ctx context.Context, req *v1.NewsCollectOpReq) (res *v1.NewsCollectOpRes, err error) {
	res = &v1.NewsCollectOpRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectOpInput{
		ArticleId: uint32(req.ArticleId),
	}
	service.Islamic().NewsCollectOp(ctx, JuzParamInput)
	return res, nil
}

func (*ControllerNews) NewsViewOp(ctx context.Context, req *v1.NewsViewOpReq) (res *v1.NewsViewOpRes, err error) {
	res = &v1.NewsViewOpRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectOpInput{
		ArticleId: uint32(req.ArticleId),
	}
	service.Islamic().NewsViewOp(ctx, JuzParamInput)
	return res, nil
}

func (*ControllerNews) NewsShareOp(ctx context.Context, req *v1.NewsShareOpReq) (res *v1.NewsShareOpRes, err error) {
	res = &v1.NewsShareOpRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectOpInput{
		ArticleId: uint32(req.ArticleId),
	}
	service.Islamic().NewsShareOp(ctx, JuzParamInput)
	return res, nil
}
