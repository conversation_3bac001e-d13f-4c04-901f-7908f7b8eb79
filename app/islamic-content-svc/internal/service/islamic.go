// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
)

type (
	IIslamic interface {
		SurahList(ctx context.Context, in *model.SurahParamInput) (out []*model.SurahParamOutput)
		JuzList(ctx context.Context, in *model.JuzParamInput) (out []*model.JuzParamOutput)
		QuerySurahByAyahId(ayahId int) (out *entity.SuratDaftar)
		QueryAyahByAyahId(ayahId int) (out *entity.SuratAyat)
		AyahList(ctx context.Context, in *model.AyahParamInput) (out []*model.AyahParamOutPut)
		AyahReadRecord(ctx context.Context, in *model.AyahReadRecordInput) (out []*model.AyahReadRecordOutput)
		AyahReadCollectList(ctx context.Context, in *model.AyahReadCollectListInput) (out []*model.AyahReadCollectListOutput)
		AyahReadCollect(ctx context.Context, in *model.AyahReadCollectInput) (out *model.AyahReadCollectOutput)
		CheckAyahReadCollectStatus(ctx context.Context, in *model.CheckAyahReadCollectStatusInput) (out *model.CheckAyahReadCollectStatusOutput)
		AyahReadRecordList(ctx context.Context, in *model.AyahReadRecordListInput) (out []*model.AyahReadRecordListOutput)
		QueryCateLanguageByIdAndLanId(CategoryId uint, LanguageId uint) (out *entity.NewsCategoryLanguage)
		NewsCategoryList(ctx context.Context, in *model.NewsCategoryListInput) (out []*model.NewsCategoryListOutput)
		QueryTopicLanguageByIdAndLanId(CategoryId uint, LanguageId uint) (out *entity.NewsTopicLanguage)
		NewsTopicList(ctx context.Context, in *model.NewsTopicListInput) (out []*model.NewsTopicListOutput)
		NewsListByCateId(ctx context.Context, in *model.NewsListByCateIdInput) (out []*model.NewsListByCateIdOutput)
		NewsInfo(ctx context.Context, in *model.NewsInfoInput) (out []*model.NewsInfoOutput)
		NewsListByTopicId(ctx context.Context, in *model.NewsListByTopicIdInput) (out []*model.NewsListByTopicIdOutput)
		NewsHotArticleIds(ctx context.Context, limit int) (articleIds []uint)
		NewsHotList(ctx context.Context, in *model.NewsHotListInput) (out []*model.NewsListByCateIdOutput)
		NewsCollectArticleIds(ctx context.Context, limit int) (articleIds []uint)
		NewsCollectList(ctx context.Context, in *model.NewsCollectListInput) (out []*model.NewsListByCateIdOutput)
		NewsCollectStatusCheck(ctx context.Context, in *model.NewsCollectStatusCheckInput) (out *model.NewsCollectStatusCheckOutput)
		QueryArticleByArticleId(article int, language int) (out *entity.NewsArticleLanguage)
		NewsCollectOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput)
		NewsViewOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput)
		NewsShareOp(ctx context.Context, in *model.NewsCollectOpInput) (out *model.NewsCollectOpOutput)
	}
)

var (
	localIslamic IIslamic
)

func Islamic() IIslamic {
	if localIslamic == nil {
		panic("implement not found for interface IIslamic, forgot register?")
	}
	return localIslamic
}

func RegisterIslamic(i IIslamic) {
	localIslamic = i
}
